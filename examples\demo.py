"""
持续学习演示脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch
import matplotlib.pyplot as plt
from src.data.data_loader import MNISTIncrementalData<PERSON>oader, IncrementalTaskManager
from src.models.cnn_model import SimpleCNN
from src.continual_learning.naive_learner import NaiveContinualLearner
from src.continual_learning.lwf_learner import LwFContinualLearner
from src.continual_learning.ewc_learner import EWCContinualLearner


def demo_data_loading():
    """演示数据加载"""
    print("=" * 60)
    print("演示1: 数据加载和任务划分")
    print("=" * 60)
    
    # 创建数据加载器
    data_loader = MNISTIncrementalDataLoader(batch_size=64)
    
    # 创建任务管理器
    task_manager = IncrementalTaskManager()
    
    # 演示不同的任务划分策略
    strategies = ['5-5', '2-2-2-2-2', '1-1-1-1-1-1-1-1-1-1']
    
    for strategy in strategies:
        print(f"\n任务划分策略: {strategy}")
        tasks = task_manager.create_split_tasks(strategy)
        
        for i, task_classes in enumerate(tasks):
            train_loader, test_loader = data_loader.get_task_data(task_classes)
            print(f"  任务 {i}: 类别 {task_classes}, 训练样本 {len(train_loader.dataset)}, 测试样本 {len(test_loader.dataset)}")
    
    print("\n数据加载演示完成！")


def demo_model_creation():
    """演示模型创建"""
    print("\n" + "=" * 60)
    print("演示2: 模型创建和结构")
    print("=" * 60)
    
    # 创建简单CNN模型
    model = SimpleCNN(num_classes=10)
    
    print("模型结构:")
    print(model)
    
    # 计算参数数量
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    print(f"\n模型参数统计:")
    print(f"  总参数数量: {total_params:,}")
    print(f"  可训练参数: {trainable_params:,}")
    
    # 测试前向传播
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = model.to(device)
    
    # 创建随机输入
    batch_size = 4
    input_tensor = torch.randn(batch_size, 1, 28, 28).to(device)
    
    with torch.no_grad():
        output = model(input_tensor)
        print(f"\n前向传播测试:")
        print(f"  输入形状: {input_tensor.shape}")
        print(f"  输出形状: {output.shape}")
        print(f"  输出范围: [{output.min().item():.3f}, {output.max().item():.3f}]")
    
    print("模型创建演示完成！")


def demo_continual_learning():
    """演示持续学习过程"""
    print("\n" + "=" * 60)
    print("演示3: 持续学习过程")
    print("=" * 60)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 创建数据加载器
    data_loader = MNISTIncrementalDataLoader(batch_size=128)
    task_manager = IncrementalTaskManager()
    tasks = task_manager.create_split_tasks('5-5')
    
    # 创建不同的学习器
    learners = {}
    
    # 朴素学习器
    model_naive = SimpleCNN(num_classes=10).to(device)
    learners['Naive'] = NaiveContinualLearner(model_naive, device, learning_rate=0.01)
    
    # LwF学习器
    model_lwf = SimpleCNN(num_classes=10).to(device)
    learners['LwF'] = LwFContinualLearner(model_lwf, device, learning_rate=0.01, 
                                         distillation_weight=1.0, temperature=4.0)
    
    # EWC学习器
    model_ewc = SimpleCNN(num_classes=10).to(device)
    learners['EWC'] = EWCContinualLearner(model_ewc, device, learning_rate=0.01,
                                         ewc_lambda=1000.0, fisher_sample_size=500)
    
    # 存储结果
    results = {name: {'task_accuracies': [], 'overall_accuracies': []} 
              for name in learners.keys()}
    
    # 逐任务训练
    for task_id, task_classes in enumerate(tasks):
        print(f"\n--- 训练任务 {task_id}: 类别 {task_classes} ---")
        
        # 获取任务数据
        train_loader, test_loader = data_loader.get_task_data(task_classes)
        
        # 训练每个学习器
        for name, learner in learners.items():
            print(f"\n训练 {name} 学习器...")
            
            # 训练任务（少量epoch用于演示）
            learner.learn_task(train_loader, task_id, epochs=3)
            
            # 评估当前任务
            task_performance = learner.evaluate(test_loader)
            results[name]['task_accuracies'].append(task_performance['accuracy'])
            
            # 评估整体性能
            full_test_loader = data_loader.get_full_test_loader()
            overall_performance = learner.evaluate(full_test_loader)
            results[name]['overall_accuracies'].append(overall_performance['accuracy'])
            
            print(f"  {name}: 任务准确率 {task_performance['accuracy']:.2f}%, "
                  f"整体准确率 {overall_performance['accuracy']:.2f}%")
    
    # 可视化结果
    plt.figure(figsize=(12, 5))
    
    # 绘制整体准确率变化
    plt.subplot(1, 2, 1)
    for name, result in results.items():
        plt.plot(range(1, len(result['overall_accuracies']) + 1), 
                result['overall_accuracies'], marker='o', label=name, linewidth=2)
    
    plt.xlabel('Task Number')
    plt.ylabel('Overall Accuracy (%)')
    plt.title('Overall Accuracy During Continual Learning')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 绘制最终任务性能对比
    plt.subplot(1, 2, 2)
    methods = list(results.keys())
    final_accuracies = [results[name]['overall_accuracies'][-1] for name in methods]
    
    bars = plt.bar(methods, final_accuracies, alpha=0.7)
    plt.ylabel('Final Overall Accuracy (%)')
    plt.title('Final Performance Comparison')
    plt.ylim(0, 100)
    
    # 添加数值标签
    for bar, acc in zip(bars, final_accuracies):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1, 
                f'{acc:.1f}%', ha='center', va='bottom')
    
    plt.tight_layout()
    plt.savefig('./demo_results.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 打印总结
    print(f"\n" + "=" * 60)
    print("演示总结")
    print("=" * 60)
    
    for name, result in results.items():
        final_acc = result['overall_accuracies'][-1]
        if len(result['overall_accuracies']) > 1:
            forgetting = result['overall_accuracies'][0] - result['overall_accuracies'][-1]
            print(f"{name}:")
            print(f"  最终整体准确率: {final_acc:.2f}%")
            print(f"  遗忘程度: {forgetting:.2f}%")
        else:
            print(f"{name}: 最终整体准确率: {final_acc:.2f}%")
    
    print("持续学习演示完成！")


def demo_forgetting_analysis():
    """演示遗忘分析"""
    print("\n" + "=" * 60)
    print("演示4: 灾难性遗忘分析")
    print("=" * 60)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 创建数据和模型
    data_loader = MNISTIncrementalDataLoader(batch_size=128)
    task_manager = IncrementalTaskManager()
    tasks = task_manager.create_split_tasks('5-5')
    
    # 只使用朴素方法来明显展示遗忘
    model = SimpleCNN(num_classes=10).to(device)
    learner = NaiveContinualLearner(model, device, learning_rate=0.01)
    
    # 存储每个任务在不同时间点的性能
    task_performances = {0: [], 1: []}
    
    for task_id, task_classes in enumerate(tasks):
        print(f"\n训练任务 {task_id}: 类别 {task_classes}")
        
        # 获取数据
        train_loader, _ = data_loader.get_task_data(task_classes)
        
        # 训练
        learner.learn_task(train_loader, task_id, epochs=5)
        
        # 评估所有已学习的任务
        for eval_task_id in range(task_id + 1):
            eval_classes = tasks[eval_task_id]
            _, eval_test_loader = data_loader.get_task_data(eval_classes)
            performance = learner.evaluate(eval_test_loader)
            task_performances[eval_task_id].append(performance['accuracy'])
            
            print(f"  任务 {eval_task_id} 准确率: {performance['accuracy']:.2f}%")
    
    # 可视化遗忘过程
    plt.figure(figsize=(10, 6))
    
    for task_id, accuracies in task_performances.items():
        learning_stages = list(range(task_id, len(accuracies) + task_id))
        plt.plot(learning_stages, accuracies, marker='o', linewidth=2, 
                label=f'Task {task_id} (Classes {tasks[task_id]})')
    
    plt.xlabel('Learning Stage')
    plt.ylabel('Task-specific Accuracy (%)')
    plt.title('Catastrophic Forgetting Analysis')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 添加垂直线标记任务边界
    plt.axvline(x=0.5, color='red', linestyle='--', alpha=0.7, label='Task Boundary')
    
    plt.tight_layout()
    plt.savefig('./forgetting_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 计算遗忘指标
    if len(task_performances[0]) > 1:
        forgetting = task_performances[0][0] - task_performances[0][-1]
        print(f"\n遗忘分析:")
        print(f"  任务0初始准确率: {task_performances[0][0]:.2f}%")
        print(f"  任务0最终准确率: {task_performances[0][-1]:.2f}%")
        print(f"  遗忘程度: {forgetting:.2f}%")
    
    print("遗忘分析演示完成！")


def main():
    """主演示函数"""
    print("持续学习项目演示")
    print("=" * 60)
    
    try:
        # 演示1: 数据加载
        demo_data_loading()
        
        # 演示2: 模型创建
        demo_model_creation()
        
        # 演示3: 持续学习过程
        demo_continual_learning()
        
        # 演示4: 遗忘分析
        demo_forgetting_analysis()
        
        print(f"\n" + "=" * 60)
        print("所有演示完成！")
        print("生成的图片:")
        print("  - demo_results.png: 持续学习结果对比")
        print("  - forgetting_analysis.png: 灾难性遗忘分析")
        print("=" * 60)
        
    except Exception as e:
        print(f"演示过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
