"""
持续学习训练器
"""

import torch
import torch.nn as nn
from torch.utils.data import DataLoader
from typing import Dict, List, Type, Any
import os
import json
import time
from datetime import datetime

from ..data.data_loader import MNISTIncrementalDataLoader, IncrementalTaskManager
from ..models.cnn_model import SimpleCNN, create_model
from ..continual_learning.base_learner import BaseContinualLearner
from ..continual_learning.naive_learner import NaiveContinualLearner
from ..continual_learning.lwf_learner import LwFContinualLearner
from ..continual_learning.ewc_learner import EWCContinualLearner


class ContinualLearningTrainer:
    """
    持续学习训练器，支持多种持续学习方法的训练和比较
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化训练器
        
        Args:
            config: 训练配置字典
        """
        self.config = config
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 创建结果保存目录
        self.results_dir = config.get('results_dir', './results')
        self.create_directories()
        
        # 初始化数据加载器
        self.data_loader = MNISTIncrementalDataLoader(
            data_dir=config.get('data_dir', './data'),
            batch_size=config.get('batch_size', 128)
        )
        
        # 初始化任务管理器
        self.task_manager = IncrementalTaskManager()
        self.tasks = self.task_manager.create_split_tasks(
            config.get('split_strategy', '5-5')
        )
        
        # 训练历史
        self.training_history = {}
        
        print(f"持续学习训练器初始化完成:")
        print(f"  设备: {self.device}")
        print(f"  任务数量: {len(self.tasks)}")
        print(f"  批次大小: {config.get('batch_size', 128)}")
        print(f"  结果保存目录: {self.results_dir}")
    
    def create_directories(self):
        """创建必要的目录"""
        os.makedirs(self.results_dir, exist_ok=True)
        os.makedirs(os.path.join(self.results_dir, 'models'), exist_ok=True)
        os.makedirs(os.path.join(self.results_dir, 'logs'), exist_ok=True)
        os.makedirs(os.path.join(self.results_dir, 'plots'), exist_ok=True)
    
    def create_learner(self, method: str, model: nn.Module) -> BaseContinualLearner:
        """
        创建持续学习器
        
        Args:
            method: 学习方法名称
            model: 神经网络模型
            
        Returns:
            learner: 持续学习器实例
        """
        method_config = self.config.get('methods', {}).get(method, {})
        
        if method == 'naive':
            return NaiveContinualLearner(
                model=model,
                device=self.device,
                learning_rate=method_config.get('learning_rate', 0.001)
            )
        elif method == 'lwf':
            return LwFContinualLearner(
                model=model,
                device=self.device,
                learning_rate=method_config.get('learning_rate', 0.001),
                distillation_weight=method_config.get('distillation_weight', 1.0),
                temperature=method_config.get('temperature', 4.0)
            )
        elif method == 'ewc':
            return EWCContinualLearner(
                model=model,
                device=self.device,
                learning_rate=method_config.get('learning_rate', 0.001),
                ewc_lambda=method_config.get('ewc_lambda', 1000.0),
                fisher_sample_size=method_config.get('fisher_sample_size', 1000)
            )
        else:
            raise ValueError(f"不支持的学习方法: {method}")
    
    def train_method(self, method: str) -> Dict:
        """
        训练指定方法
        
        Args:
            method: 学习方法名称
            
        Returns:
            results: 训练结果
        """
        print(f"\n{'='*60}")
        print(f"开始训练方法: {method.upper()}")
        print(f"{'='*60}")
        
        # 创建模型
        model_config = self.config.get('model', {})
        model = create_model(
            model_type=model_config.get('type', 'simple'),
            num_classes=model_config.get('num_classes', 10),
            dropout_rate=model_config.get('dropout_rate', 0.5)
        )
        
        # 创建学习器
        learner = self.create_learner(method, model)
        
        # 训练结果
        results = {
            'method': method,
            'tasks': [],
            'evaluations': [],
            'training_time': 0,
            'model_info': learner.get_method_info()
        }
        
        start_time = time.time()
        
        # 逐任务训练
        for task_id, task_classes in enumerate(self.tasks):
            print(f"\n--- 训练任务 {task_id}: 类别 {task_classes} ---")
            
            # 获取任务数据
            train_loader, test_loader = self.data_loader.get_task_data(task_classes)
            
            # 训练任务
            task_stats = learner.learn_task(
                train_loader=train_loader,
                task_id=task_id,
                epochs=self.config.get('epochs_per_task', 10)
            )
            
            results['tasks'].append(task_stats)
            
            # 评估所有任务
            evaluation = self.evaluate_all_tasks(learner, task_id)
            results['evaluations'].append(evaluation)
            
            # 保存中间模型
            model_path = os.path.join(
                self.results_dir, 'models', 
                f'{method}_task_{task_id}.pth'
            )
            learner.save_model(model_path)
        
        results['training_time'] = time.time() - start_time
        
        print(f"\n方法 {method} 训练完成，总耗时: {results['training_time']:.2f}秒")
        
        return results
    
    def evaluate_all_tasks(self, learner: BaseContinualLearner, current_task: int) -> Dict:
        """
        评估所有已学习任务的性能
        
        Args:
            learner: 持续学习器
            current_task: 当前任务ID
            
        Returns:
            evaluation: 评估结果
        """
        evaluation = {
            'current_task': current_task,
            'task_performances': {},
            'overall_performance': {},
            'forgetting_metrics': {}
        }
        
        # 评估每个已学习的任务
        for task_id in range(current_task + 1):
            task_classes = self.tasks[task_id]
            _, test_loader = self.data_loader.get_task_data(task_classes)
            
            performance = learner.evaluate(test_loader)
            evaluation['task_performances'][task_id] = performance
        
        # 评估整体性能（所有类别）
        full_test_loader = self.data_loader.get_full_test_loader()
        overall_performance = learner.evaluate(full_test_loader)
        evaluation['overall_performance'] = overall_performance
        
        # 计算遗忘指标
        if current_task > 0:
            # 计算平均准确率
            task_accuracies = [evaluation['task_performances'][tid]['accuracy'] 
                             for tid in range(current_task + 1)]
            evaluation['forgetting_metrics']['average_accuracy'] = sum(task_accuracies) / len(task_accuracies)
            
            # 计算遗忘程度（第一个任务的准确率下降）
            first_task_acc = evaluation['task_performances'][0]['accuracy']
            if 0 in learner.performance_history:
                initial_acc = learner.performance_history[0]['accuracy']
                evaluation['forgetting_metrics']['first_task_forgetting'] = initial_acc - first_task_acc
            
            # 计算后向迁移
            evaluation['forgetting_metrics']['backward_transfer'] = self.compute_backward_transfer(
                evaluation['task_performances']
            )
        
        return evaluation
    
    def compute_backward_transfer(self, task_performances: Dict) -> float:
        """
        计算后向迁移指标
        
        Args:
            task_performances: 任务性能字典
            
        Returns:
            backward_transfer: 后向迁移值
        """
        if len(task_performances) < 2:
            return 0.0
        
        # 简化的后向迁移计算
        accuracies = [task_performances[tid]['accuracy'] for tid in sorted(task_performances.keys())]
        
        # 计算相对于第一个任务的平均下降
        first_acc = accuracies[0]
        avg_acc = sum(accuracies) / len(accuracies)
        
        return avg_acc - first_acc
    
    def run_experiments(self, methods: List[str]) -> Dict:
        """
        运行多个方法的对比实验
        
        Args:
            methods: 要比较的方法列表
            
        Returns:
            all_results: 所有方法的结果
        """
        print(f"开始运行持续学习对比实验...")
        print(f"比较方法: {', '.join(methods)}")
        
        all_results = {}
        
        for method in methods:
            try:
                results = self.train_method(method)
                all_results[method] = results
                
                # 保存单个方法的结果
                self.save_results(method, results)
                
            except Exception as e:
                print(f"方法 {method} 训练失败: {str(e)}")
                continue
        
        # 保存对比结果
        self.save_comparison_results(all_results)
        
        return all_results
    
    def save_results(self, method: str, results: Dict):
        """
        保存单个方法的结果
        
        Args:
            method: 方法名称
            results: 结果字典
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{method}_results_{timestamp}.json"
        filepath = os.path.join(self.results_dir, 'logs', filename)
        
        # 转换tensor为可序列化的格式
        serializable_results = self.make_serializable(results)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(serializable_results, f, indent=2, ensure_ascii=False)
        
        print(f"结果已保存到: {filepath}")
    
    def save_comparison_results(self, all_results: Dict):
        """
        保存对比结果
        
        Args:
            all_results: 所有方法的结果
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"comparison_results_{timestamp}.json"
        filepath = os.path.join(self.results_dir, 'logs', filename)
        
        # 转换tensor为可序列化的格式
        serializable_results = self.make_serializable(all_results)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(serializable_results, f, indent=2, ensure_ascii=False)
        
        print(f"对比结果已保存到: {filepath}")
    
    def make_serializable(self, obj):
        """
        将对象转换为可序列化的格式
        
        Args:
            obj: 要转换的对象
            
        Returns:
            serializable_obj: 可序列化的对象
        """
        if isinstance(obj, torch.Tensor):
            return obj.item() if obj.numel() == 1 else obj.tolist()
        elif isinstance(obj, dict):
            return {key: self.make_serializable(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [self.make_serializable(item) for item in obj]
        else:
            return obj
