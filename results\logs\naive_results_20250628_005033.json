{"method": "naive", "tasks": [{"task_id": 0, "method": "Naive", "epochs": 2, "epoch_stats": [{"loss": 0.2716104224720865, "accuracy": 90.09020787030984, "correct": 27564, "total": 30596}, {"loss": 0.13938461909553376, "accuracy": 94.96993070989672, "correct": 29057, "total": 30596}], "final_loss": 0.13938461909553376, "final_accuracy": 94.96993070989672}, {"task_id": 1, "method": "Naive", "epochs": 2, "epoch_stats": [{"loss": 1.1224243838825951, "accuracy": 56.74738130866549, "correct": 16686, "total": 29404}, {"loss": 0.3322902320195799, "accuracy": 89.57624812950618, "correct": 26339, "total": 29404}], "final_loss": 0.3322902320195799, "final_accuracy": 89.57624812950618}], "evaluations": [{"current_task": 0, "task_performances": {"0": {"loss": 0.03711785895006322, "accuracy": 99.2605565285075, "correct": 5101, "total": 5139, "class_accuracies": {"0": 100.0, "1": 99.73568281938326, "2": 97.38372093023256, "3": 99.20792079207921, "4": 100.0}}}, "overall_performance": {"loss": 9.93607619158022, "accuracy": 51.01, "correct": 5101, "total": 10000, "class_accuracies": {"7": 0.0, "2": 97.38372093023256, "1": 99.73568281938326, "0": 100.0, "4": 100.0, "9": 0.0, "5": 0.0, "6": 0.0, "3": 99.20792079207921, "8": 0.0}}, "forgetting_metrics": {}}, {"current_task": 1, "task_performances": {"0": {"loss": 11.467212700549467, "accuracy": 0.0, "correct": 0, "total": 5139, "class_accuracies": {"0": 0.0, "1": 0.0, "2": 0.0, "3": 0.0, "4": 0.0}}, "1": {"loss": 0.07061734179162878, "accuracy": 98.35424809709936, "correct": 4781, "total": 4861, "class_accuracies": {"5": 96.97309417040358, "6": 98.95615866388309, "7": 96.88715953307393, "8": 99.79466119096509, "9": 99.10802775024777}}}, "overall_performance": {"loss": 5.92448911241665, "accuracy": 47.81, "correct": 4781, "total": 10000, "class_accuracies": {"7": 96.88715953307393, "2": 0.0, "1": 0.0, "0": 0.0, "4": 0.0, "9": 99.10802775024777, "5": 96.97309417040358, "6": 98.95615866388309, "3": 0.0, "8": 99.79466119096509}}, "forgetting_metrics": {"average_accuracy": 49.17712404854968, "backward_transfer": 49.17712404854968}}], "training_time": 166.38391494750977, "model_info": {"name": "Naive", "description": "朴素持续学习方法，直接在新数据上继续训练", "advantages": ["实现简单", "训练速度快", "内存开销小"], "disadvantages": ["严重的灾难性遗忘", "无法保持旧知识", "性能随任务数量下降"], "parameters": {"learning_rate": 0.01, "optimizer": "<PERSON>", "criterion": "CrossEntropyLoss"}}}