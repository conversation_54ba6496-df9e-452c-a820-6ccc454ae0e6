"""
持续学习结果可视化
"""

import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
import pandas as pd
from typing import Dict, List, Optional
import os


class ContinualLearningVisualizer:
    """
    持续学习结果可视化器
    """
    
    def __init__(self, save_dir: str = './results/plots'):
        """
        初始化可视化器
        
        Args:
            save_dir: 图片保存目录
        """
        self.save_dir = save_dir
        os.makedirs(save_dir, exist_ok=True)
        
        # 设置绘图风格
        plt.style.use('seaborn-v0_8')
        sns.set_palette("husl")
        
        # 中文字体设置（如果需要）
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
    
    def plot_accuracy_comparison(self, all_results: Dict, save_name: str = 'accuracy_comparison.png'):
        """
        绘制准确率对比图
        
        Args:
            all_results: 所有方法的结果
            save_name: 保存文件名
        """
        plt.figure(figsize=(12, 8))
        
        for method, results in all_results.items():
            evaluations = results['evaluations']
            
            # 提取整体准确率趋势
            tasks = []
            accuracies = []
            
            for eval_data in evaluations:
                task_id = eval_data['current_task']
                accuracy = eval_data['overall_performance']['accuracy']
                tasks.append(task_id + 1)  # 任务编号从1开始
                accuracies.append(accuracy)
            
            plt.plot(tasks, accuracies, marker='o', linewidth=2, 
                    label=f'{method.upper()}', markersize=8)
        
        plt.xlabel('Task Number', fontsize=12)
        plt.ylabel('Overall Accuracy (%)', fontsize=12)
        plt.title('Continual Learning: Overall Accuracy Comparison', fontsize=14, fontweight='bold')
        plt.legend(fontsize=11)
        plt.grid(True, alpha=0.3)
        plt.ylim(0, 100)
        
        # 添加任务分界线
        for i in range(1, len(tasks)):
            plt.axvline(x=i+0.5, color='gray', linestyle='--', alpha=0.5)
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.save_dir, save_name), dpi=300, bbox_inches='tight')
        plt.show()
        print(f"准确率对比图已保存: {save_name}")
    
    def plot_forgetting_analysis(self, all_results: Dict, save_name: str = 'forgetting_analysis.png'):
        """
        绘制遗忘分析图
        
        Args:
            all_results: 所有方法的结果
            save_name: 保存文件名
        """
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # 1. 各方法的平均遗忘对比
        methods = []
        avg_forgetting = []
        max_forgetting = []
        
        for method, results in all_results.items():
            methods.append(method.upper())
            
            # 计算平均遗忘
            evaluations = results['evaluations']
            if len(evaluations) > 1:
                # 简化的遗忘计算
                first_task_accs = []
                for eval_data in evaluations:
                    if 0 in eval_data['task_performances']:
                        first_task_accs.append(eval_data['task_performances'][0]['accuracy'])
                
                if len(first_task_accs) > 1:
                    forgetting = max(first_task_accs) - first_task_accs[-1]
                    avg_forgetting.append(forgetting)
                    max_forgetting.append(forgetting)
                else:
                    avg_forgetting.append(0)
                    max_forgetting.append(0)
            else:
                avg_forgetting.append(0)
                max_forgetting.append(0)
        
        # 绘制遗忘对比柱状图
        x_pos = np.arange(len(methods))
        axes[0, 0].bar(x_pos, avg_forgetting, alpha=0.7)
        axes[0, 0].set_xlabel('Methods')
        axes[0, 0].set_ylabel('Average Forgetting (%)')
        axes[0, 0].set_title('Average Forgetting Comparison')
        axes[0, 0].set_xticks(x_pos)
        axes[0, 0].set_xticklabels(methods)
        
        # 2. 第一个任务的准确率变化
        for method, results in all_results.items():
            evaluations = results['evaluations']
            tasks = []
            first_task_accs = []
            
            for eval_data in evaluations:
                if 0 in eval_data['task_performances']:
                    task_id = eval_data['current_task']
                    accuracy = eval_data['task_performances'][0]['accuracy']
                    tasks.append(task_id + 1)
                    first_task_accs.append(accuracy)
            
            if tasks and first_task_accs:
                axes[0, 1].plot(tasks, first_task_accs, marker='o', 
                               label=f'{method.upper()}', linewidth=2)
        
        axes[0, 1].set_xlabel('Task Number')
        axes[0, 1].set_ylabel('First Task Accuracy (%)')
        axes[0, 1].set_title('First Task Performance Over Time')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)
        
        # 3. 任务特定的准确率热力图（以第一个方法为例）
        if all_results:
            first_method = list(all_results.keys())[0]
            evaluations = all_results[first_method]['evaluations']
            
            # 构建热力图数据
            max_tasks = len(evaluations)
            heatmap_data = np.zeros((max_tasks, max_tasks))
            
            for i, eval_data in enumerate(evaluations):
                for task_id, performance in eval_data['task_performances'].items():
                    if task_id < max_tasks:
                        heatmap_data[i, task_id] = performance['accuracy']
            
            # 只显示有效数据
            mask = np.triu(np.ones_like(heatmap_data, dtype=bool), k=1)
            
            sns.heatmap(heatmap_data, mask=mask, annot=True, fmt='.1f', 
                       cmap='YlOrRd', ax=axes[1, 0], cbar_kws={'label': 'Accuracy (%)'})
            axes[1, 0].set_xlabel('Task ID')
            axes[1, 0].set_ylabel('Learning Stage')
            axes[1, 0].set_title(f'Task Performance Matrix ({first_method.upper()})')
        
        # 4. 训练时间对比
        methods_time = []
        training_times = []
        
        for method, results in all_results.items():
            methods_time.append(method.upper())
            training_times.append(results.get('training_time', 0))
        
        x_pos = np.arange(len(methods_time))
        axes[1, 1].bar(x_pos, training_times, alpha=0.7, color='skyblue')
        axes[1, 1].set_xlabel('Methods')
        axes[1, 1].set_ylabel('Training Time (seconds)')
        axes[1, 1].set_title('Training Time Comparison')
        axes[1, 1].set_xticks(x_pos)
        axes[1, 1].set_xticklabels(methods_time)
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.save_dir, save_name), dpi=300, bbox_inches='tight')
        plt.show()
        print(f"遗忘分析图已保存: {save_name}")
    
    def plot_learning_curves(self, all_results: Dict, save_name: str = 'learning_curves.png'):
        """
        绘制学习曲线
        
        Args:
            all_results: 所有方法的结果
            save_name: 保存文件名
        """
        n_methods = len(all_results)
        fig, axes = plt.subplots(1, n_methods, figsize=(5*n_methods, 5))
        
        if n_methods == 1:
            axes = [axes]
        
        for idx, (method, results) in enumerate(all_results.items()):
            tasks = results['tasks']
            
            for task_data in tasks:
                task_id = task_data['task_id']
                epoch_stats = task_data['epoch_stats']
                
                epochs = list(range(1, len(epoch_stats) + 1))
                accuracies = [stats['accuracy'] for stats in epoch_stats]
                losses = [stats.get('total_loss', stats.get('loss', 0)) for stats in epoch_stats]
                
                # 绘制准确率曲线
                ax1 = axes[idx]
                color = f'C{task_id}'
                ax1.plot(epochs, accuracies, color=color, linewidth=2, 
                        label=f'Task {task_id} Accuracy')
                ax1.set_xlabel('Epoch')
                ax1.set_ylabel('Accuracy (%)', color='blue')
                ax1.tick_params(axis='y', labelcolor='blue')
                ax1.set_title(f'{method.upper()} Learning Curves')
                
                # 绘制损失曲线
                ax2 = ax1.twinx()
                ax2.plot(epochs, losses, color=color, linestyle='--', alpha=0.7,
                        label=f'Task {task_id} Loss')
                ax2.set_ylabel('Loss', color='red')
                ax2.tick_params(axis='y', labelcolor='red')
            
            ax1.legend(loc='upper left')
            ax1.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.save_dir, save_name), dpi=300, bbox_inches='tight')
        plt.show()
        print(f"学习曲线图已保存: {save_name}")
    
    def plot_confusion_matrix(self, model, test_loader, class_names: List[str], 
                            save_name: str = 'confusion_matrix.png'):
        """
        绘制混淆矩阵
        
        Args:
            model: 训练好的模型
            test_loader: 测试数据加载器
            class_names: 类别名称列表
            save_name: 保存文件名
        """
        model.eval()
        all_preds = []
        all_targets = []
        
        with torch.no_grad():
            for data, target in test_loader:
                output = model(data)
                pred = output.argmax(dim=1)
                all_preds.extend(pred.cpu().numpy())
                all_targets.extend(target.cpu().numpy())
        
        # 计算混淆矩阵
        cm = confusion_matrix(all_targets, all_preds)
        
        # 绘制混淆矩阵
        plt.figure(figsize=(10, 8))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                   xticklabels=class_names, yticklabels=class_names)
        plt.xlabel('Predicted Label')
        plt.ylabel('True Label')
        plt.title('Confusion Matrix')
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.save_dir, save_name), dpi=300, bbox_inches='tight')
        plt.show()
        print(f"混淆矩阵已保存: {save_name}")
    
    def plot_method_comparison_radar(self, comparison: Dict, save_name: str = 'method_radar.png'):
        """
        绘制方法对比雷达图
        
        Args:
            comparison: 方法比较结果
            save_name: 保存文件名
        """
        methods = comparison['methods']
        
        # 定义评估维度
        dimensions = ['Final Accuracy', 'Avg Task Accuracy', 'Low Forgetting', 'Training Speed']
        
        # 准备数据
        data = []
        for method in methods:
            perf = comparison['performance_comparison'].get(method, {})
            forget = comparison['forgetting_comparison'].get(method, {})
            efficiency = comparison['efficiency_comparison'].get(method, {})
            
            # 归一化指标（0-1）
            final_acc = perf.get('final_accuracy', 0) / 100
            avg_acc = perf.get('average_task_accuracy', 0) / 100
            low_forget = 1 - (forget.get('average_forgetting', 0) / 100)  # 遗忘越少越好
            
            # 训练速度（时间越短越好）
            max_time = max(comparison['efficiency_comparison'][m].get('total_training_time', 1) 
                          for m in methods)
            speed = 1 - (efficiency.get('total_training_time', max_time) / max_time)
            
            data.append([final_acc, avg_acc, low_forget, speed])
        
        # 绘制雷达图
        angles = np.linspace(0, 2 * np.pi, len(dimensions), endpoint=False).tolist()
        angles += angles[:1]  # 闭合图形
        
        fig, ax = plt.subplots(figsize=(10, 10), subplot_kw=dict(projection='polar'))
        
        for i, method in enumerate(methods):
            values = data[i] + data[i][:1]  # 闭合数据
            ax.plot(angles, values, 'o-', linewidth=2, label=method.upper())
            ax.fill(angles, values, alpha=0.25)
        
        ax.set_xticks(angles[:-1])
        ax.set_xticklabels(dimensions)
        ax.set_ylim(0, 1)
        ax.set_title('Method Comparison Radar Chart', size=16, fontweight='bold', pad=20)
        ax.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
        ax.grid(True)
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.save_dir, save_name), dpi=300, bbox_inches='tight')
        plt.show()
        print(f"雷达图已保存: {save_name}")
    
    def create_comprehensive_report(self, all_results: Dict, comparison: Dict):
        """
        创建综合可视化报告
        
        Args:
            all_results: 所有方法的结果
            comparison: 方法比较结果
        """
        print("生成综合可视化报告...")
        
        # 生成各种图表
        self.plot_accuracy_comparison(all_results)
        self.plot_forgetting_analysis(all_results)
        self.plot_learning_curves(all_results)
        self.plot_method_comparison_radar(comparison)
        
        print(f"所有图表已保存到: {self.save_dir}")


# 导入torch（如果需要混淆矩阵功能）
try:
    import torch
except ImportError:
    print("警告: 未安装PyTorch，混淆矩阵功能将不可用")
