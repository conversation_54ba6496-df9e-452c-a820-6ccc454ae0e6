"""
快速运行实验脚本
"""

import os
import sys
import subprocess
import time

def check_dependencies():
    """检查依赖是否安装"""
    print("检查依赖...")

    # 包名映射：安装名 -> 导入名
    package_mapping = {
        'torch': 'torch',
        'torchvision': 'torchvision',
        'numpy': 'numpy',
        'matplotlib': 'matplotlib',
        'seaborn': 'seaborn',
        'scikit-learn': 'sklearn',  # 安装名是scikit-learn，导入名是sklearn
        'tqdm': 'tqdm',
        'pandas': 'pandas'
    }

    missing_packages = []

    for install_name, import_name in package_mapping.items():
        try:
            module = __import__(import_name)
            version = getattr(module, '__version__', 'Unknown')
            print(f"✓ {install_name} (导入为 {import_name}) - 版本: {version}")
        except ImportError:
            missing_packages.append(install_name)
            print(f"✗ {install_name} (导入为 {import_name}) - 缺失")

    if missing_packages:
        print(f"\n缺失的包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        print("或者单独安装: pip install " + " ".join(missing_packages))
        return False

    print("\n✓ 所有依赖已正确安装！")
    return True


def create_directories():
    """创建必要的目录"""
    directories = [
        'data',
        'results',
        'results/models',
        'results/logs',
        'results/plots'
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"创建目录: {directory}")


def run_quick_test():
    """运行快速测试"""
    print("\n" + "="*50)
    print("运行快速测试...")
    print("="*50)
    
    try:
        # 运行快速演示
        result = subprocess.run([
            sys.executable, 'main.py', 'demo'
        ], capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("✓ 快速测试成功！")
            print(result.stdout)
        else:
            print("✗ 快速测试失败！")
            print("错误输出:")
            print(result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("✗ 快速测试超时！")
        return False
    except Exception as e:
        print(f"✗ 快速测试出错: {str(e)}")
        return False
    
    return True


def run_full_experiment():
    """运行完整实验"""
    print("\n" + "="*50)
    print("运行完整实验...")
    print("="*50)
    
    try:
        # 运行完整实验
        result = subprocess.run([
            sys.executable, 'main.py', 
            '--config', 'base',
            '--methods', 'naive', 'lwf', 'ewc',
            '--verbose'
        ], timeout=1800)  # 30分钟超时
        
        if result.returncode == 0:
            print("✓ 完整实验成功！")
        else:
            print("✗ 完整实验失败！")
            return False
            
    except subprocess.TimeoutExpired:
        print("✗ 完整实验超时！")
        return False
    except Exception as e:
        print(f"✗ 完整实验出错: {str(e)}")
        return False
    
    return True


def run_demo_visualization():
    """运行演示可视化"""
    print("\n" + "="*50)
    print("运行演示可视化...")
    print("="*50)
    
    try:
        result = subprocess.run([
            sys.executable, 'examples/demo.py'
        ], timeout=600)  # 10分钟超时
        
        if result.returncode == 0:
            print("✓ 演示可视化成功！")
        else:
            print("✗ 演示可视化失败！")
            return False
            
    except subprocess.TimeoutExpired:
        print("✗ 演示可视化超时！")
        return False
    except Exception as e:
        print(f"✗ 演示可视化出错: {str(e)}")
        return False
    
    return True


def main():
    """主函数"""
    print("持续学习项目实验运行器")
    print("="*50)
    
    # 检查依赖
    if not check_dependencies():
        return
    
    # 创建目录
    create_directories()
    
    # 询问用户要运行什么
    print("\n请选择要运行的实验:")
    print("1. 快速测试 (约2-3分钟)")
    print("2. 完整实验 (约15-30分钟)")
    print("3. 演示可视化 (约5-10分钟)")
    print("4. 全部运行")
    print("0. 退出")
    
    while True:
        choice = input("\n请输入选择 (0-4): ").strip()
        
        if choice == '0':
            print("退出程序")
            break
        elif choice == '1':
            run_quick_test()
            break
        elif choice == '2':
            run_full_experiment()
            break
        elif choice == '3':
            run_demo_visualization()
            break
        elif choice == '4':
            print("运行所有实验...")
            
            # 依次运行所有实验
            success = True
            
            if success:
                success = run_quick_test()
            
            if success:
                success = run_demo_visualization()
            
            if success:
                success = run_full_experiment()
            
            if success:
                print("\n" + "="*50)
                print("所有实验完成！")
                print("="*50)
                print("结果文件位置:")
                print("- results/logs/: 实验日志和结果")
                print("- results/plots/: 可视化图表")
                print("- results/models/: 训练好的模型")
                print("- demo_results.png: 演示结果图")
                print("- forgetting_analysis.png: 遗忘分析图")
            else:
                print("\n实验过程中出现错误，请检查输出信息")
            
            break
        else:
            print("无效选择，请重新输入")


if __name__ == "__main__":
    main()
