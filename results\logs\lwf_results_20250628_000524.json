{"method": "lwf", "tasks": [{"task_id": 0, "method": "LwF", "epochs": 10, "epoch_stats": [{"total_loss": 0.2663243527257388, "ce_loss": 0.2663243527257388, "distill_loss": 0.0, "accuracy": 89.57380049679696, "correct": 27406, "total": 30596}, {"total_loss": 0.027125744071311905, "ce_loss": 0.027125744071311905, "distill_loss": 0.0, "accuracy": 99.21558373643613, "correct": 30356, "total": 30596}, {"total_loss": 0.018063394919742373, "ce_loss": 0.018063394919742373, "distill_loss": 0.0, "accuracy": 99.48032422538894, "correct": 30437, "total": 30596}, {"total_loss": 0.016017643366649282, "ce_loss": 0.016017643366649282, "distill_loss": 0.0, "accuracy": 99.55549745064714, "correct": 30460, "total": 30596}, {"total_loss": 0.01344583272360372, "ce_loss": 0.01344583272360372, "distill_loss": 0.0, "accuracy": 99.68296509347627, "correct": 30499, "total": 30596}, {"total_loss": 0.01576765234148875, "ce_loss": 0.01576765234148875, "distill_loss": 0.0, "accuracy": 99.55876585174532, "correct": 30461, "total": 30596}, {"total_loss": 0.00702807645672389, "ce_loss": 0.00702807645672389, "distill_loss": 0.0, "accuracy": 99.80716433520722, "correct": 30537, "total": 30596}, {"total_loss": 0.006778244377930302, "ce_loss": 0.006778244377930302, "distill_loss": 0.0, "accuracy": 99.78428552751994, "correct": 30530, "total": 30596}, {"total_loss": 0.004870955287609983, "ce_loss": 0.004870955287609983, "distill_loss": 0.0, "accuracy": 99.86272715387632, "correct": 30554, "total": 30596}, {"total_loss": 0.0051899126026739555, "ce_loss": 0.0051899126026739555, "distill_loss": 0.0, "accuracy": 99.84965354948359, "correct": 30550, "total": 30596}], "final_loss": 0.0051899126026739555, "final_accuracy": 99.84965354948359}, {"task_id": 1, "method": "LwF", "epochs": 10, "epoch_stats": [{"total_loss": 8.975100125437198, "ce_loss": 3.174090965934422, "distill_loss": 5.801009170905403, "accuracy": 6.842606448102299, "correct": 2012, "total": 29404}, {"total_loss": 7.58501932517342, "ce_loss": 2.6264356975970062, "distill_loss": 4.958583645198656, "accuracy": 9.13481159025983, "correct": 2686, "total": 29404}, {"total_loss": 7.36776272524958, "ce_loss": 2.5711111669955047, "distill_loss": 4.796651566546896, "accuracy": 9.437491497755408, "correct": 2775, "total": 29404}, {"total_loss": 7.216623513594918, "ce_loss": 2.5332785720410556, "distill_loss": 4.683344942590464, "accuracy": 9.376275336688886, "correct": 2757, "total": 29404}, {"total_loss": 7.114778433675351, "ce_loss": 2.5149601386940996, "distill_loss": 4.599818312603494, "accuracy": 9.995238743028159, "correct": 2939, "total": 29404}, {"total_loss": 7.054434585571289, "ce_loss": 2.483857334178427, "distill_loss": 4.570577261758888, "accuracy": 10.05645490409468, "correct": 2957, "total": 29404}, {"total_loss": 6.996853034392647, "ce_loss": 2.472114512194758, "distill_loss": 4.524738506648852, "accuracy": 10.535981499115767, "correct": 3098, "total": 29404}, {"total_loss": 6.9604137358458145, "ce_loss": 2.4603816260462223, "distill_loss": 4.5000321129094, "accuracy": 10.42375187049381, "correct": 3065, "total": 29404}, {"total_loss": 6.930099058151245, "ce_loss": 2.4542478613231493, "distill_loss": 4.475851207194121, "accuracy": 10.501972520745477, "correct": 3088, "total": 29404}, {"total_loss": 6.885264427765556, "ce_loss": 2.4546690816464634, "distill_loss": 4.430595326423645, "accuracy": 10.498571622908448, "correct": 3087, "total": 29404}], "final_loss": 6.885264427765556, "final_accuracy": 10.498571622908448}], "evaluations": [{"current_task": 0, "task_performances": {"0": {"loss": 0.003837514684459256, "accuracy": 99.90270480638256, "correct": 5134, "total": 5139, "class_accuracies": {"0": 100.0, "1": 99.91189427312776, "2": 99.70930232558139, "3": 100.0, "4": 99.89816700610999}}}, "overall_performance": {"loss": 9.638595472408246, "accuracy": 51.34, "correct": 5134, "total": 10000, "class_accuracies": {"7": 0.0, "2": 99.70930232558139, "1": 99.91189427312776, "0": 100.0, "4": 99.89816700610999, "9": 0.0, "5": 0.0, "6": 0.0, "3": 100.0, "8": 0.0}}, "forgetting_metrics": {}}, {"current_task": 1, "task_performances": {"0": {"loss": 0.03799565353930542, "accuracy": 99.49406499318934, "correct": 5113, "total": 5139, "class_accuracies": {"0": 100.0, "1": 99.29515418502203, "2": 98.44961240310077, "3": 100.0, "4": 99.79633401221996}}, "1": {"loss": 2.144711491308714, "accuracy": 9.23678255502983, "correct": 449, "total": 4861, "class_accuracies": {"5": 0.0, "6": 1.7745302713987474, "7": 37.7431906614786, "8": 1.3347022587268993, "9": 3.072348860257681}}}, "overall_performance": {"loss": 1.0624109317984762, "accuracy": 55.62, "correct": 5562, "total": 10000, "class_accuracies": {"7": 37.7431906614786, "2": 98.44961240310077, "1": 99.29515418502203, "0": 100.0, "4": 99.79633401221996, "9": 3.072348860257681, "5": 0.0, "6": 1.7745302713987474, "3": 100.0, "8": 1.3347022587268993}}, "forgetting_metrics": {"average_accuracy": 54.36542377410959, "backward_transfer": -45.12864121907975}}], "training_time": 691.1006112098694, "model_info": {"name": "LwF", "description": "Learning without Forgetting，使用知识蒸馏保持旧知识", "advantages": ["有效缓解灾难性遗忘", "不需要存储旧数据", "计算开销适中"], "disadvantages": ["需要保存旧模型", "蒸馏温度需要调优", "对新任务学习可能有影响"], "parameters": {"learning_rate": 0.001, "distillation_weight": 1.0, "temperature": 4.0, "optimizer": "<PERSON>", "criterion": "CrossEntropyLoss"}}}