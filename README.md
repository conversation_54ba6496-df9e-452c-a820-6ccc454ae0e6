# MNIST类增量学习实验

基于MNIST数据集的类增量学习（Class-Incremental Learning）实验项目。

## 项目简介

本项目实现了一个完整的类增量学习实验框架，模拟智能系统逐步学习新类别的场景：
- 初始训练只使用部分数字（0-4）
- 后续逐步加入新的类别（5-9）
- 评估模型在学习新知识时对旧知识的保持能力

## 项目结构

```
continus/
├── README.md                 # 项目说明文档
├── requirements.txt          # 依赖包列表
├── config/
│   └── experiment_config.py  # 实验配置文件
├── src/
│   ├── __init__.py
│   ├── data/
│   │   ├── __init__.py
│   │   └── data_loader.py    # 数据加载和预处理
│   ├── models/
│   │   ├── __init__.py
│   │   └── cnn_model.py      # CNN模型定义
│   ├── continual_learning/
│   │   ├── __init__.py
│   │   ├── base_learner.py   # 基础学习器
│   │   ├── naive_learner.py  # 朴素持续学习
│   │   ├── lwf_learner.py    # Learning without Forgetting
│   │   └── ewc_learner.py    # Elastic Weight Consolidation
│   ├── training/
│   │   ├── __init__.py
│   │   └── trainer.py        # 训练器
│   └── evaluation/
│       ├── __init__.py
│       ├── evaluator.py      # 评估器
│       └── visualizer.py     # 结果可视化
├── experiments/
│   └── run_experiment.py     # 主实验脚本
└── results/                  # 实验结果保存目录
    ├── models/               # 保存的模型
    ├── logs/                 # 训练日志
    └── plots/                # 可视化图表
```

## 实验场景

模拟交通摄像头系统的数字识别能力逐步扩展：
1. **第1阶段**：系统初始只能识别数字0-4
2. **第2阶段**：系统需要学习识别数字5-9，同时保持对0-4的识别能力

## 持续学习方法

1. **朴素方法（Naive）**：直接在新数据上继续训练，展示灾难性遗忘现象
2. **Learning without Forgetting (LwF)**：使用知识蒸馏保持旧知识
3. **Elastic Weight Consolidation (EWC)**：通过Fisher信息矩阵保护重要参数

## 快速开始

### 方法1: 使用运行脚本（推荐）
```bash
python run_experiment.py
```
然后按照提示选择要运行的实验类型。

### 方法2: 手动运行

1. 安装依赖：
```bash
pip install -r requirements.txt
```

2. 运行基础实验：
```bash
python main.py --config base --methods naive lwf ewc
```

3. 运行快速演示：
```bash
python main.py demo
```

4. 运行可视化演示：
```bash
python examples/demo.py
```

### 实验配置选项

- **配置类型**：
  - `base`: 基础配置（10 epochs/task）
  - `quick`: 快速测试（2 epochs/task）
  - `full`: 完整实验（20 epochs/task）
  - `lwf_ablation`: LwF温度参数消融实验
  - `ewc_ablation`: EWC lambda参数消融实验

- **学习方法**：
  - `naive`: 朴素持续学习（无遗忘保护）
  - `lwf`: Learning without Forgetting（知识蒸馏）
  - `ewc`: Elastic Weight Consolidation（弹性权重固化）

- **任务划分策略**：
  - `5-5`: 两个任务，每个5个类别
  - `2-2-2-2-2`: 五个任务，每个2个类别
  - `1-1-1-1-1-1-1-1-1-1`: 十个任务，每个1个类别

### 查看结果
实验结果将保存在 `results/` 目录下，包括：
- `logs/`: 训练日志和详细结果
- `models/`: 训练好的模型检查点
- `plots/`: 性能可视化图表

## 评估指标

- **整体准确率**：在全部类别（0-9）测试集上的准确率
- **旧类别保持率**：在旧类别（0-4）上的准确率保持情况
- **新类别学习率**：在新类别（5-9）上的学习效果
- **遗忘程度**：量化灾难性遗忘的严重程度

## 实验结果分析

项目将生成以下可视化结果：
1. 不同方法的准确率对比曲线
2. 各类别的混淆矩阵
3. 遗忘程度的定量分析
4. 训练过程的损失函数变化

## 扩展功能

- 支持更多持续学习算法
- 可配置的任务划分策略
- 详细的性能分析工具
- 实验结果的自动报告生成
