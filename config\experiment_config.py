"""
实验配置文件
"""

import os

# 基础配置
BASE_CONFIG = {
    # 数据配置
    'data_dir': './data',
    'batch_size': 128,
    'split_strategy': '5-5',  # 任务划分策略: '5-5', '2-2-2-2-2', '1-1-1-1-1-1-1-1-1-1'
    
    # 模型配置
    'model': {
        'type': 'simple',  # 'simple' 或 'adaptive'
        'num_classes': 10,
        'dropout_rate': 0.5
    },
    
    # 训练配置
    'epochs_per_task': 10,
    'results_dir': './results',
    
    # 方法配置
    'methods': {
        'naive': {
            'learning_rate': 0.001
        },
        'lwf': {
            'learning_rate': 0.001,
            'distillation_weight': 1.0,
            'temperature': 4.0
        },
        'ewc': {
            'learning_rate': 0.001,
            'ewc_lambda': 1000.0,
            'fisher_sample_size': 1000
        }
    }
}

# 快速测试配置（用于调试）
QUICK_TEST_CONFIG = {
    **BASE_CONFIG,
    'epochs_per_task': 2,
    'batch_size': 64,
    'methods': {
        'naive': {
            'learning_rate': 0.01
        },
        'lwf': {
            'learning_rate': 0.01,
            'distillation_weight': 0.5,
            'temperature': 2.0
        }
    }
}

# 完整实验配置
FULL_EXPERIMENT_CONFIG = {
    **BASE_CONFIG,
    'epochs_per_task': 20,
    'batch_size': 256,
    'methods': {
        'naive': {
            'learning_rate': 0.001
        },
        'lwf': {
            'learning_rate': 0.001,
            'distillation_weight': 2.0,
            'temperature': 4.0
        },
        'ewc': {
            'learning_rate': 0.001,
            'ewc_lambda': 5000.0,
            'fisher_sample_size': 2000
        }
    }
}

# 消融实验配置（不同超参数）
ABLATION_CONFIGS = {
    'lwf_temperature': {
        **BASE_CONFIG,
        'epochs_per_task': 15,
        'methods': {
            'lwf_t2': {
                'learning_rate': 0.001,
                'distillation_weight': 1.0,
                'temperature': 2.0
            },
            'lwf_t4': {
                'learning_rate': 0.001,
                'distillation_weight': 1.0,
                'temperature': 4.0
            },
            'lwf_t8': {
                'learning_rate': 0.001,
                'distillation_weight': 1.0,
                'temperature': 8.0
            }
        }
    },
    'ewc_lambda': {
        **BASE_CONFIG,
        'epochs_per_task': 15,
        'methods': {
            'ewc_100': {
                'learning_rate': 0.001,
                'ewc_lambda': 100.0,
                'fisher_sample_size': 1000
            },
            'ewc_1000': {
                'learning_rate': 0.001,
                'ewc_lambda': 1000.0,
                'fisher_sample_size': 1000
            },
            'ewc_10000': {
                'learning_rate': 0.001,
                'ewc_lambda': 10000.0,
                'fisher_sample_size': 1000
            }
        }
    }
}

# 不同任务划分策略的配置
TASK_SPLIT_CONFIGS = {
    'split_5_5': {
        **BASE_CONFIG,
        'split_strategy': '5-5'
    },
    'split_2_2_2_2_2': {
        **BASE_CONFIG,
        'split_strategy': '2-2-2-2-2',
        'epochs_per_task': 8
    },
    'split_1_each': {
        **BASE_CONFIG,
        'split_strategy': '1-1-1-1-1-1-1-1-1-1',
        'epochs_per_task': 5
    }
}


def get_config(config_name: str = 'base'):
    """
    获取指定的配置
    
    Args:
        config_name: 配置名称
        
    Returns:
        config: 配置字典
    """
    configs = {
        'base': BASE_CONFIG,
        'quick': QUICK_TEST_CONFIG,
        'full': FULL_EXPERIMENT_CONFIG,
        'lwf_ablation': ABLATION_CONFIGS['lwf_temperature'],
        'ewc_ablation': ABLATION_CONFIGS['ewc_lambda'],
        'split_5_5': TASK_SPLIT_CONFIGS['split_5_5'],
        'split_2_2_2_2_2': TASK_SPLIT_CONFIGS['split_2_2_2_2_2'],
        'split_1_each': TASK_SPLIT_CONFIGS['split_1_each']
    }
    
    if config_name not in configs:
        print(f"警告: 未找到配置 '{config_name}'，使用默认配置")
        return BASE_CONFIG
    
    return configs[config_name]


def print_config(config: dict, title: str = "实验配置"):
    """
    打印配置信息
    
    Args:
        config: 配置字典
        title: 标题
    """
    print(f"\n{'='*50}")
    print(f"{title}")
    print(f"{'='*50}")
    
    print(f"数据配置:")
    print(f"  数据目录: {config.get('data_dir', 'N/A')}")
    print(f"  批次大小: {config.get('batch_size', 'N/A')}")
    print(f"  任务划分: {config.get('split_strategy', 'N/A')}")
    
    print(f"\n模型配置:")
    model_config = config.get('model', {})
    print(f"  模型类型: {model_config.get('type', 'N/A')}")
    print(f"  类别数量: {model_config.get('num_classes', 'N/A')}")
    print(f"  Dropout率: {model_config.get('dropout_rate', 'N/A')}")
    
    print(f"\n训练配置:")
    print(f"  每任务训练轮数: {config.get('epochs_per_task', 'N/A')}")
    print(f"  结果保存目录: {config.get('results_dir', 'N/A')}")
    
    print(f"\n方法配置:")
    methods = config.get('methods', {})
    for method_name, method_config in methods.items():
        print(f"  {method_name.upper()}:")
        for param, value in method_config.items():
            print(f"    {param}: {value}")
    
    print(f"{'='*50}")


def validate_config(config: dict) -> bool:
    """
    验证配置的有效性
    
    Args:
        config: 配置字典
        
    Returns:
        is_valid: 配置是否有效
    """
    required_keys = ['data_dir', 'batch_size', 'split_strategy', 'model', 'epochs_per_task', 'methods']
    
    for key in required_keys:
        if key not in config:
            print(f"错误: 缺少必需的配置项 '{key}'")
            return False
    
    # 验证方法配置
    methods = config['methods']
    if not methods:
        print("错误: 至少需要配置一种学习方法")
        return False
    
    # 验证模型配置
    model_config = config['model']
    required_model_keys = ['type', 'num_classes']
    for key in required_model_keys:
        if key not in model_config:
            print(f"错误: 模型配置缺少 '{key}'")
            return False
    
    print("配置验证通过")
    return True


if __name__ == "__main__":
    # 测试配置
    print("测试实验配置...")
    
    # 测试基础配置
    base_config = get_config('base')
    print_config(base_config, "基础配置")
    validate_config(base_config)
    
    # 测试快速配置
    quick_config = get_config('quick')
    print_config(quick_config, "快速测试配置")
    validate_config(quick_config)
    
    print("配置测试完成！")
