"""
快速测试scikit-learn导入
"""

print("测试scikit-learn导入...")

try:
    import sklearn
    print(f"✓ sklearn导入成功，版本: {sklearn.__version__}")
    
    # 测试常用的sklearn模块
    from sklearn.metrics import confusion_matrix, classification_report
    print("✓ sklearn.metrics导入成功")
    
    from sklearn.model_selection import train_test_split
    print("✓ sklearn.model_selection导入成功")
    
    print("\n✓ scikit-learn完全可用！")
    
except ImportError as e:
    print(f"✗ sklearn导入失败: {e}")
    print("请尝试安装: pip install scikit-learn")

print("\n测试其他关键依赖...")

packages = ['torch', 'numpy', 'matplotlib']
for pkg in packages:
    try:
        module = __import__(pkg)
        version = getattr(module, '__version__', 'Unknown')
        print(f"✓ {pkg} - 版本: {version}")
    except ImportError:
        print(f"✗ {pkg} - 未安装")

print("\n测试完成！")
