"""
持续学习评估器
"""

import torch
import numpy as np
from typing import Dict, List, Tuple
from sklearn.metrics import confusion_matrix, classification_report
import json
import os


class ContinualLearningEvaluator:
    """
    持续学习评估器，提供各种评估指标的计算
    """
    
    def __init__(self):
        """初始化评估器"""
        self.metrics_history = []
    
    def evaluate_method_results(self, results: Dict) -> Dict:
        """
        评估单个方法的结果
        
        Args:
            results: 方法训练结果
            
        Returns:
            evaluation_metrics: 评估指标
        """
        method_name = results['method']
        evaluations = results['evaluations']
        
        print(f"\n=== 评估方法: {method_name.upper()} ===")
        
        evaluation_metrics = {
            'method': method_name,
            'task_metrics': [],
            'overall_metrics': {},
            'forgetting_analysis': {},
            'learning_efficiency': {}
        }
        
        # 分析每个任务的性能
        for eval_data in evaluations:
            task_id = eval_data['current_task']
            task_metrics = self.analyze_task_performance(eval_data, task_id)
            evaluation_metrics['task_metrics'].append(task_metrics)
        
        # 计算整体指标
        evaluation_metrics['overall_metrics'] = self.compute_overall_metrics(evaluations)
        
        # 分析遗忘情况
        evaluation_metrics['forgetting_analysis'] = self.analyze_forgetting(evaluations)
        
        # 分析学习效率
        evaluation_metrics['learning_efficiency'] = self.analyze_learning_efficiency(results)
        
        return evaluation_metrics
    
    def analyze_task_performance(self, eval_data: Dict, task_id: int) -> Dict:
        """
        分析单个任务的性能
        
        Args:
            eval_data: 评估数据
            task_id: 任务ID
            
        Returns:
            task_metrics: 任务指标
        """
        task_performances = eval_data['task_performances']
        overall_performance = eval_data['overall_performance']
        
        task_metrics = {
            'task_id': task_id,
            'individual_task_accuracies': {},
            'overall_accuracy': overall_performance['accuracy'],
            'class_accuracies': overall_performance.get('class_accuracies', {}),
            'num_learned_tasks': len(task_performances)
        }
        
        # 记录每个任务的准确率
        for tid, performance in task_performances.items():
            task_metrics['individual_task_accuracies'][tid] = performance['accuracy']
        
        return task_metrics
    
    def compute_overall_metrics(self, evaluations: List[Dict]) -> Dict:
        """
        计算整体性能指标
        
        Args:
            evaluations: 评估历史
            
        Returns:
            overall_metrics: 整体指标
        """
        if not evaluations:
            return {}
        
        final_evaluation = evaluations[-1]
        
        # 最终整体准确率
        final_accuracy = final_evaluation['overall_performance']['accuracy']
        
        # 平均任务准确率
        task_accuracies = list(final_evaluation['task_performances'].values())
        avg_task_accuracy = sum(acc['accuracy'] for acc in task_accuracies) / len(task_accuracies)
        
        # 准确率变化趋势
        accuracy_trend = [eval_data['overall_performance']['accuracy'] for eval_data in evaluations]
        
        overall_metrics = {
            'final_accuracy': final_accuracy,
            'average_task_accuracy': avg_task_accuracy,
            'accuracy_trend': accuracy_trend,
            'accuracy_std': np.std(accuracy_trend),
            'max_accuracy': max(accuracy_trend),
            'min_accuracy': min(accuracy_trend)
        }
        
        return overall_metrics
    
    def analyze_forgetting(self, evaluations: List[Dict]) -> Dict:
        """
        分析遗忘情况
        
        Args:
            evaluations: 评估历史
            
        Returns:
            forgetting_analysis: 遗忘分析结果
        """
        if len(evaluations) < 2:
            return {'forgetting_detected': False}
        
        forgetting_analysis = {
            'forgetting_detected': True,
            'task_forgetting': {},
            'average_forgetting': 0.0,
            'max_forgetting': 0.0,
            'forgetting_trend': []
        }
        
        # 分析每个任务的遗忘情况
        for task_id in range(len(evaluations) - 1):
            task_forgetting = []
            
            # 获取该任务在不同时间点的性能
            for eval_idx in range(task_id, len(evaluations)):
                if task_id in evaluations[eval_idx]['task_performances']:
                    accuracy = evaluations[eval_idx]['task_performances'][task_id]['accuracy']
                    task_forgetting.append(accuracy)
            
            if len(task_forgetting) > 1:
                # 计算遗忘程度（最高性能 - 最终性能）
                max_acc = max(task_forgetting)
                final_acc = task_forgetting[-1]
                forgetting = max_acc - final_acc
                
                forgetting_analysis['task_forgetting'][task_id] = {
                    'max_accuracy': max_acc,
                    'final_accuracy': final_acc,
                    'forgetting_amount': forgetting,
                    'accuracy_history': task_forgetting
                }
        
        # 计算平均遗忘和最大遗忘
        if forgetting_analysis['task_forgetting']:
            forgetting_amounts = [info['forgetting_amount'] 
                                for info in forgetting_analysis['task_forgetting'].values()]
            forgetting_analysis['average_forgetting'] = np.mean(forgetting_amounts)
            forgetting_analysis['max_forgetting'] = max(forgetting_amounts)
        
        # 计算遗忘趋势
        for eval_data in evaluations:
            if 'forgetting_metrics' in eval_data and 'average_accuracy' in eval_data['forgetting_metrics']:
                forgetting_analysis['forgetting_trend'].append(
                    eval_data['forgetting_metrics']['average_accuracy']
                )
        
        return forgetting_analysis
    
    def analyze_learning_efficiency(self, results: Dict) -> Dict:
        """
        分析学习效率
        
        Args:
            results: 训练结果
            
        Returns:
            efficiency_metrics: 效率指标
        """
        tasks = results['tasks']
        training_time = results['training_time']
        
        efficiency_metrics = {
            'total_training_time': training_time,
            'average_time_per_task': training_time / len(tasks) if tasks else 0,
            'convergence_analysis': {},
            'parameter_efficiency': {}
        }
        
        # 分析每个任务的收敛情况
        for task_data in tasks:
            task_id = task_data['task_id']
            epoch_stats = task_data['epoch_stats']
            
            if epoch_stats:
                # 计算收敛速度（达到90%最终准确率所需的epoch数）
                final_accuracy = epoch_stats[-1]['accuracy']
                target_accuracy = 0.9 * final_accuracy
                
                convergence_epoch = len(epoch_stats)
                for i, stats in enumerate(epoch_stats):
                    if stats['accuracy'] >= target_accuracy:
                        convergence_epoch = i + 1
                        break
                
                efficiency_metrics['convergence_analysis'][task_id] = {
                    'final_accuracy': final_accuracy,
                    'convergence_epoch': convergence_epoch,
                    'total_epochs': len(epoch_stats),
                    'convergence_ratio': convergence_epoch / len(epoch_stats)
                }
        
        return efficiency_metrics
    
    def compare_methods(self, all_results: Dict) -> Dict:
        """
        比较多个方法的性能
        
        Args:
            all_results: 所有方法的结果
            
        Returns:
            comparison: 比较结果
        """
        print(f"\n=== 方法对比分析 ===")
        
        comparison = {
            'methods': list(all_results.keys()),
            'performance_comparison': {},
            'forgetting_comparison': {},
            'efficiency_comparison': {},
            'ranking': {}
        }
        
        # 评估每个方法
        method_evaluations = {}
        for method, results in all_results.items():
            method_evaluations[method] = self.evaluate_method_results(results)
        
        # 性能比较
        comparison['performance_comparison'] = self.compare_performance(method_evaluations)
        
        # 遗忘比较
        comparison['forgetting_comparison'] = self.compare_forgetting(method_evaluations)
        
        # 效率比较
        comparison['efficiency_comparison'] = self.compare_efficiency(method_evaluations)
        
        # 综合排名
        comparison['ranking'] = self.rank_methods(method_evaluations)
        
        return comparison
    
    def compare_performance(self, method_evaluations: Dict) -> Dict:
        """
        比较性能指标
        
        Args:
            method_evaluations: 方法评估结果
            
        Returns:
            performance_comparison: 性能比较
        """
        performance_comparison = {}
        
        for method, evaluation in method_evaluations.items():
            overall_metrics = evaluation['overall_metrics']
            performance_comparison[method] = {
                'final_accuracy': overall_metrics.get('final_accuracy', 0),
                'average_task_accuracy': overall_metrics.get('average_task_accuracy', 0),
                'max_accuracy': overall_metrics.get('max_accuracy', 0),
                'accuracy_std': overall_metrics.get('accuracy_std', 0)
            }
        
        # 找出最佳性能
        best_final = max(performance_comparison.items(), 
                        key=lambda x: x[1]['final_accuracy'])
        best_average = max(performance_comparison.items(), 
                          key=lambda x: x[1]['average_task_accuracy'])
        
        performance_comparison['best_final_accuracy'] = best_final
        performance_comparison['best_average_accuracy'] = best_average
        
        return performance_comparison
    
    def compare_forgetting(self, method_evaluations: Dict) -> Dict:
        """
        比较遗忘情况
        
        Args:
            method_evaluations: 方法评估结果
            
        Returns:
            forgetting_comparison: 遗忘比较
        """
        forgetting_comparison = {}
        
        for method, evaluation in method_evaluations.items():
            forgetting_analysis = evaluation['forgetting_analysis']
            forgetting_comparison[method] = {
                'average_forgetting': forgetting_analysis.get('average_forgetting', 0),
                'max_forgetting': forgetting_analysis.get('max_forgetting', 0),
                'forgetting_detected': forgetting_analysis.get('forgetting_detected', False)
            }
        
        # 找出遗忘最少的方法
        methods_with_forgetting = {k: v for k, v in forgetting_comparison.items() 
                                 if v['forgetting_detected']}
        
        if methods_with_forgetting:
            least_forgetting = min(methods_with_forgetting.items(), 
                                 key=lambda x: x[1]['average_forgetting'])
            forgetting_comparison['least_forgetting'] = least_forgetting
        
        return forgetting_comparison
    
    def compare_efficiency(self, method_evaluations: Dict) -> Dict:
        """
        比较效率指标
        
        Args:
            method_evaluations: 方法评估结果
            
        Returns:
            efficiency_comparison: 效率比较
        """
        efficiency_comparison = {}
        
        for method, evaluation in method_evaluations.items():
            efficiency_metrics = evaluation['learning_efficiency']
            efficiency_comparison[method] = {
                'total_training_time': efficiency_metrics.get('total_training_time', 0),
                'average_time_per_task': efficiency_metrics.get('average_time_per_task', 0)
            }
        
        # 找出最快的方法
        fastest_method = min(efficiency_comparison.items(), 
                           key=lambda x: x[1]['total_training_time'])
        efficiency_comparison['fastest_method'] = fastest_method
        
        return efficiency_comparison
    
    def rank_methods(self, method_evaluations: Dict) -> Dict:
        """
        对方法进行综合排名
        
        Args:
            method_evaluations: 方法评估结果
            
        Returns:
            ranking: 排名结果
        """
        ranking = {}
        
        # 定义权重
        weights = {
            'final_accuracy': 0.4,
            'average_forgetting': 0.3,  # 越小越好
            'training_time': 0.3        # 越小越好
        }
        
        method_scores = {}
        
        for method, evaluation in method_evaluations.items():
            score = 0
            
            # 最终准确率得分
            final_acc = evaluation['overall_metrics'].get('final_accuracy', 0)
            score += weights['final_accuracy'] * (final_acc / 100)
            
            # 遗忘程度得分（越小越好）
            avg_forgetting = evaluation['forgetting_analysis'].get('average_forgetting', 0)
            forgetting_score = max(0, 1 - avg_forgetting / 100)  # 归一化
            score += weights['average_forgetting'] * forgetting_score
            
            # 训练时间得分（越小越好）
            training_time = evaluation['learning_efficiency'].get('total_training_time', float('inf'))
            if training_time != float('inf'):
                # 简单的时间归一化（假设最长时间为基准）
                max_time = max(eval_data['learning_efficiency'].get('total_training_time', 0) 
                             for eval_data in method_evaluations.values())
                time_score = 1 - (training_time / max_time) if max_time > 0 else 1
                score += weights['training_time'] * time_score
            
            method_scores[method] = score
        
        # 排序
        sorted_methods = sorted(method_scores.items(), key=lambda x: x[1], reverse=True)
        
        ranking = {
            'scores': method_scores,
            'sorted_ranking': sorted_methods,
            'best_method': sorted_methods[0] if sorted_methods else None,
            'weights_used': weights
        }
        
        return ranking
    
    def generate_summary_report(self, comparison: Dict) -> str:
        """
        生成总结报告
        
        Args:
            comparison: 比较结果
            
        Returns:
            report: 文本报告
        """
        report = []
        report.append("=" * 60)
        report.append("持续学习方法对比报告")
        report.append("=" * 60)
        
        # 参与比较的方法
        methods = comparison['methods']
        report.append(f"\n参与比较的方法: {', '.join(methods)}")
        
        # 性能比较
        performance = comparison['performance_comparison']
        report.append(f"\n【性能比较】")
        for method in methods:
            if method in performance:
                perf = performance[method]
                report.append(f"  {method.upper()}:")
                report.append(f"    最终准确率: {perf['final_accuracy']:.2f}%")
                report.append(f"    平均任务准确率: {perf['average_task_accuracy']:.2f}%")
        
        # 遗忘比较
        forgetting = comparison['forgetting_comparison']
        report.append(f"\n【遗忘比较】")
        for method in methods:
            if method in forgetting:
                forget = forgetting[method]
                report.append(f"  {method.upper()}:")
                report.append(f"    平均遗忘: {forget['average_forgetting']:.2f}%")
                report.append(f"    最大遗忘: {forget['max_forgetting']:.2f}%")
        
        # 综合排名
        ranking = comparison['ranking']
        if 'sorted_ranking' in ranking:
            report.append(f"\n【综合排名】")
            for i, (method, score) in enumerate(ranking['sorted_ranking'], 1):
                report.append(f"  {i}. {method.upper()} (得分: {score:.3f})")
        
        return "\n".join(report)
