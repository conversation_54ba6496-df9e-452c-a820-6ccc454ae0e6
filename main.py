"""
持续学习项目主程序
"""

import argparse
import sys
import os
import json
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.experiment_config import get_config, print_config, validate_config
from src.training.trainer import ContinualLearningTrainer
from src.evaluation.evaluator import ContinualLearningEvaluator
from src.evaluation.visualizer import ContinualLearningVisualizer


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='持续学习实验')
    parser.add_argument('--config', type=str, default='base',
                       help='配置名称 (base, quick, full, lwf_ablation, ewc_ablation)')
    parser.add_argument('--methods', type=str, nargs='+', default=['naive', 'lwf', 'ewc'],
                       help='要比较的方法列表')
    parser.add_argument('--no-visualization', action='store_true',
                       help='跳过可视化')
    parser.add_argument('--save-models', action='store_true',
                       help='保存训练好的模型')
    parser.add_argument('--verbose', action='store_true',
                       help='详细输出')
    
    args = parser.parse_args()
    
    # 获取配置
    config = get_config(args.config)
    
    # 打印配置信息
    if args.verbose:
        print_config(config, f"实验配置 ({args.config})")
    
    # 验证配置
    if not validate_config(config):
        print("配置验证失败，退出程序")
        return
    
    # 检查方法是否在配置中
    available_methods = list(config['methods'].keys())
    invalid_methods = [m for m in args.methods if m not in available_methods]
    if invalid_methods:
        print(f"错误: 以下方法未在配置中定义: {invalid_methods}")
        print(f"可用方法: {available_methods}")
        return
    
    print(f"\n开始持续学习实验...")
    print(f"配置: {args.config}")
    print(f"比较方法: {', '.join(args.methods)}")
    print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 创建训练器
        trainer = ContinualLearningTrainer(config)
        
        # 运行实验
        all_results = trainer.run_experiments(args.methods)
        
        if not all_results:
            print("实验失败，没有获得任何结果")
            return
        
        print(f"\n实验完成！成功训练了 {len(all_results)} 种方法")
        
        # 创建评估器
        evaluator = ContinualLearningEvaluator()
        
        # 进行方法对比
        comparison = evaluator.compare_methods(all_results)
        
        # 生成总结报告
        report = evaluator.generate_summary_report(comparison)
        print(f"\n{report}")
        
        # 保存对比结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        comparison_file = os.path.join(config['results_dir'], 'logs', f'final_comparison_{timestamp}.json')
        
        # 转换为可序列化格式
        serializable_comparison = trainer.make_serializable(comparison)
        with open(comparison_file, 'w', encoding='utf-8') as f:
            json.dump(serializable_comparison, f, indent=2, ensure_ascii=False)
        
        print(f"详细对比结果已保存到: {comparison_file}")
        
        # 可视化结果
        if not args.no_visualization:
            print(f"\n生成可视化图表...")
            visualizer = ContinualLearningVisualizer(
                save_dir=os.path.join(config['results_dir'], 'plots')
            )
            visualizer.create_comprehensive_report(all_results, comparison)
        
        # 打印最终排名
        ranking = comparison.get('ranking', {})
        if 'sorted_ranking' in ranking:
            print(f"\n{'='*50}")
            print(f"最终排名:")
            print(f"{'='*50}")
            for i, (method, score) in enumerate(ranking['sorted_ranking'], 1):
                print(f"{i}. {method.upper()} (综合得分: {score:.3f})")
            print(f"{'='*50}")
        
        print(f"\n实验完成！所有结果已保存到: {config['results_dir']}")
        
    except KeyboardInterrupt:
        print(f"\n实验被用户中断")
    except Exception as e:
        print(f"\n实验过程中发生错误: {str(e)}")
        if args.verbose:
            import traceback
            traceback.print_exc()


def run_quick_demo():
    """运行快速演示"""
    print("运行快速演示...")
    
    # 使用快速配置
    config = get_config('quick')
    
    # 只比较两种方法
    methods = ['naive', 'lwf']
    
    try:
        trainer = ContinualLearningTrainer(config)
        all_results = trainer.run_experiments(methods)
        
        if all_results:
            evaluator = ContinualLearningEvaluator()
            comparison = evaluator.compare_methods(all_results)
            report = evaluator.generate_summary_report(comparison)
            print(f"\n{report}")
            
            print("快速演示完成！")
        else:
            print("快速演示失败")
            
    except Exception as e:
        print(f"快速演示出错: {str(e)}")


def run_ablation_study():
    """运行消融实验"""
    print("运行消融实验...")
    
    # LwF温度参数消融实验
    print("\n1. LwF温度参数消融实验")
    lwf_config = get_config('lwf_ablation')
    trainer = ContinualLearningTrainer(lwf_config)
    lwf_results = trainer.run_experiments(['lwf_t2', 'lwf_t4', 'lwf_t8'])
    
    # EWC lambda参数消融实验
    print("\n2. EWC lambda参数消融实验")
    ewc_config = get_config('ewc_ablation')
    trainer = ContinualLearningTrainer(ewc_config)
    ewc_results = trainer.run_experiments(['ewc_100', 'ewc_1000', 'ewc_10000'])
    
    print("消融实验完成！")


if __name__ == "__main__":
    # 检查命令行参数
    if len(sys.argv) == 1:
        # 没有参数时显示帮助
        print("持续学习项目")
        print("="*50)
        print("使用方法:")
        print("  python main.py --config base --methods naive lwf ewc")
        print("  python main.py --config quick --methods naive lwf")
        print("  python main.py --config full --methods naive lwf ewc --save-models")
        print("")
        print("特殊模式:")
        print("  python main.py demo    # 运行快速演示")
        print("  python main.py ablation # 运行消融实验")
        print("")
        print("配置选项:")
        print("  base, quick, full, lwf_ablation, ewc_ablation")
        print("  split_5_5, split_2_2_2_2_2, split_1_each")
        print("")
        print("方法选项:")
        print("  naive, lwf, ewc")
        print("="*50)
        
        # 询问是否运行演示
        response = input("是否运行快速演示？(y/n): ").lower().strip()
        if response in ['y', 'yes', '是']:
            run_quick_demo()
    
    elif len(sys.argv) == 2 and sys.argv[1] == 'demo':
        run_quick_demo()
    
    elif len(sys.argv) == 2 and sys.argv[1] == 'ablation':
        run_ablation_study()
    
    else:
        main()
