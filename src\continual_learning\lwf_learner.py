"""
Learning without Forgetting (LwF) 持续学习方法
使用知识蒸馏保持旧知识
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import DataLoader
from typing import Dict, Optional
import copy
from .base_learner import BaseContinualLearner


class LwFContinualLearner(BaseContinualLearner):
    """
    Learning without Forgetting 持续学习器
    使用知识蒸馏技术保持对旧任务的记忆
    """
    
    def __init__(self, model: nn.Module, device: torch.device, learning_rate: float = 0.001,
                 distillation_weight: float = 1.0, temperature: float = 4.0):
        """
        初始化LwF持续学习器
        
        Args:
            model: 神经网络模型
            device: 计算设备
            learning_rate: 学习率
            distillation_weight: 蒸馏损失权重
            temperature: 蒸馏温度
        """
        super().__init__(model, device, learning_rate)
        self.method_name = "LwF"
        
        # LwF特有参数
        self.distillation_weight = distillation_weight
        self.temperature = temperature
        
        # 保存旧模型用于知识蒸馏
        self.old_model = None
        
        print(f"初始化LwF持续学习器:")
        print(f"  学习率: {learning_rate}")
        print(f"  蒸馏权重: {distillation_weight}")
        print(f"  蒸馏温度: {temperature}")
    
    def before_task(self, task_id: int, train_loader: DataLoader):
        """
        任务开始前的准备工作
        
        Args:
            task_id: 任务ID
            train_loader: 训练数据加载器
        """
        print(f"\n=== 开始任务 {task_id} (LwF方法) ===")
        
        # 记录任务信息
        task_info = {
            'task_id': task_id,
            'method': self.method_name,
            'num_samples': len(train_loader.dataset)
        }
        
        # 获取任务中的类别信息
        classes_in_task = set()
        for _, targets in train_loader:
            classes_in_task.update(targets.numpy().tolist())
        
        task_info['classes'] = sorted(list(classes_in_task))
        self.task_history.append(task_info)
        
        print(f"任务 {task_id} 包含类别: {task_info['classes']}")
        print(f"训练样本数量: {task_info['num_samples']}")
        
        # 如果不是第一个任务，保存当前模型作为旧模型用于蒸馏
        if task_id > 0:
            print("保存当前模型用于知识蒸馏...")
            self.old_model = copy.deepcopy(self.model)
            self.old_model.eval()
            
            # 冻结旧模型参数
            for param in self.old_model.parameters():
                param.requires_grad = False
        else:
            print("第一个任务，无需知识蒸馏")
            self.old_model = None
    
    def after_task(self, task_id: int):
        """
        任务完成后的处理工作
        
        Args:
            task_id: 任务ID
        """
        print(f"任务 {task_id} 完成 (LwF方法)")
        self.current_task = task_id
    
    def distillation_loss(self, old_outputs: torch.Tensor, new_outputs: torch.Tensor) -> torch.Tensor:
        """
        计算知识蒸馏损失
        
        Args:
            old_outputs: 旧模型的输出
            new_outputs: 新模型的输出
            
        Returns:
            distill_loss: 蒸馏损失
        """
        # 确保输出维度一致
        min_classes = min(old_outputs.size(1), new_outputs.size(1))
        old_outputs = old_outputs[:, :min_classes]
        new_outputs = new_outputs[:, :min_classes]
        
        # 使用温度软化的softmax
        old_soft = F.softmax(old_outputs / self.temperature, dim=1)
        new_log_soft = F.log_softmax(new_outputs / self.temperature, dim=1)
        
        # KL散度损失
        distill_loss = F.kl_div(new_log_soft, old_soft, reduction='batchmean')
        
        # 温度平方缩放
        distill_loss *= (self.temperature ** 2)
        
        return distill_loss
    
    def learn_task(self, train_loader: DataLoader, task_id: int, epochs: int = 10) -> Dict:
        """
        学习新任务
        
        Args:
            train_loader: 训练数据加载器
            task_id: 任务ID
            epochs: 训练轮数
            
        Returns:
            training_stats: 训练统计信息
        """
        # 任务开始前的准备
        self.before_task(task_id, train_loader)
        
        # 训练统计
        training_stats = {
            'task_id': task_id,
            'method': self.method_name,
            'epochs': epochs,
            'epoch_stats': []
        }
        
        print(f"开始训练任务 {task_id}，共 {epochs} 个epoch")
        
        # 训练循环
        for epoch in range(epochs):
            epoch_stats = self.train_epoch_with_distillation(train_loader, epoch)
            training_stats['epoch_stats'].append(epoch_stats)
            
            # 打印训练进度
            if (epoch + 1) % 5 == 0 or epoch == 0:
                if self.old_model is not None:
                    print(f"Epoch {epoch+1}/{epochs}: "
                          f"Total Loss: {epoch_stats['total_loss']:.4f}, "
                          f"CE Loss: {epoch_stats['ce_loss']:.4f}, "
                          f"Distill Loss: {epoch_stats['distill_loss']:.4f}, "
                          f"Accuracy: {epoch_stats['accuracy']:.2f}%")
                else:
                    print(f"Epoch {epoch+1}/{epochs}: "
                          f"Loss: {epoch_stats['total_loss']:.4f}, "
                          f"Accuracy: {epoch_stats['accuracy']:.2f}%")
        
        # 任务完成后的处理
        self.after_task(task_id)
        
        # 记录最终训练统计
        final_stats = training_stats['epoch_stats'][-1]
        training_stats['final_loss'] = final_stats['total_loss']
        training_stats['final_accuracy'] = final_stats['accuracy']
        
        print(f"任务 {task_id} 训练完成:")
        print(f"  最终损失: {training_stats['final_loss']:.4f}")
        print(f"  最终准确率: {training_stats['final_accuracy']:.2f}%")
        
        return training_stats

    def train_epoch_with_distillation(self, train_loader: DataLoader, epoch: int) -> Dict[str, float]:
        """
        使用知识蒸馏训练一个epoch

        Args:
            train_loader: 训练数据加载器
            epoch: 当前epoch

        Returns:
            epoch_stats: epoch统计信息
        """
        self.model.train()
        total_loss = 0.0
        total_ce_loss = 0.0
        total_distill_loss = 0.0
        correct = 0
        total = 0

        for batch_idx, (data, target) in enumerate(train_loader):
            data, target = data.to(self.device), target.to(self.device)

            self.optimizer.zero_grad()

            # 新模型前向传播
            new_outputs = self.model(data)

            # 计算交叉熵损失
            ce_loss = self.criterion(new_outputs, target)

            # 计算总损失
            total_loss_batch = ce_loss

            # 如果有旧模型，计算蒸馏损失
            distill_loss_batch = torch.tensor(0.0).to(self.device)
            if self.old_model is not None:
                with torch.no_grad():
                    old_outputs = self.old_model(data)

                distill_loss_batch = self.distillation_loss(old_outputs, new_outputs)
                total_loss_batch = ce_loss + self.distillation_weight * distill_loss_batch

            # 反向传播
            total_loss_batch.backward()
            self.optimizer.step()

            # 统计
            total_loss += total_loss_batch.item()
            total_ce_loss += ce_loss.item()
            total_distill_loss += distill_loss_batch.item()

            pred = new_outputs.argmax(dim=1)
            correct += pred.eq(target).sum().item()
            total += target.size(0)

        avg_total_loss = total_loss / len(train_loader)
        avg_ce_loss = total_ce_loss / len(train_loader)
        avg_distill_loss = total_distill_loss / len(train_loader)
        accuracy = 100.0 * correct / total

        return {
            'total_loss': avg_total_loss,
            'ce_loss': avg_ce_loss,
            'distill_loss': avg_distill_loss,
            'accuracy': accuracy,
            'correct': correct,
            'total': total
        }

    def get_method_info(self) -> Dict:
        """
        获取方法信息

        Returns:
            method_info: 方法信息字典
        """
        return {
            'name': self.method_name,
            'description': 'Learning without Forgetting，使用知识蒸馏保持旧知识',
            'advantages': [
                '有效缓解灾难性遗忘',
                '不需要存储旧数据',
                '计算开销适中'
            ],
            'disadvantages': [
                '需要保存旧模型',
                '蒸馏温度需要调优',
                '对新任务学习可能有影响'
            ],
            'parameters': {
                'learning_rate': self.learning_rate,
                'distillation_weight': self.distillation_weight,
                'temperature': self.temperature,
                'optimizer': type(self.optimizer).__name__,
                'criterion': type(self.criterion).__name__
            }
        }

    def analyze_forgetting(self, test_loaders: Dict[int, DataLoader]) -> Dict:
        """
        分析遗忘情况

        Args:
            test_loaders: 各任务的测试数据加载器字典

        Returns:
            forgetting_analysis: 遗忘分析结果
        """
        print(f"\n=== 分析遗忘情况 (LwF方法) ===")

        forgetting_analysis = {
            'method': self.method_name,
            'task_performances': {},
            'forgetting_metrics': {}
        }

        # 评估每个任务的性能
        for task_id, test_loader in test_loaders.items():
            print(f"评估任务 {task_id} 性能...")
            performance = self.evaluate(test_loader)
            forgetting_analysis['task_performances'][task_id] = performance

            print(f"任务 {task_id}: 准确率 {performance['accuracy']:.2f}%")

        # 计算遗忘指标
        if len(forgetting_analysis['task_performances']) > 1:
            task_ids = sorted(forgetting_analysis['task_performances'].keys())

            # 计算平均遗忘
            accuracies = [forgetting_analysis['task_performances'][tid]['accuracy']
                         for tid in task_ids]

            forgetting_analysis['forgetting_metrics'] = {
                'average_accuracy': sum(accuracies) / len(accuracies),
                'accuracy_drop': accuracies[0] - accuracies[-1] if len(accuracies) > 1 else 0,
                'task_accuracies': dict(zip(task_ids, accuracies))
            }

            print(f"平均准确率: {forgetting_analysis['forgetting_metrics']['average_accuracy']:.2f}%")
            print(f"准确率下降: {forgetting_analysis['forgetting_metrics']['accuracy_drop']:.2f}%")

        return forgetting_analysis


if __name__ == "__main__":
    # 测试LwF持续学习器
    print("测试LwF持续学习器...")

    # 创建简单的测试模型
    from ..models.cnn_model import SimpleCNN

    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = SimpleCNN(num_classes=10)

    # 创建LwF学习器
    learner = LwFContinualLearner(
        model, device,
        learning_rate=0.001,
        distillation_weight=1.0,
        temperature=4.0
    )

    # 打印方法信息
    method_info = learner.get_method_info()
    print(f"\n方法: {method_info['name']}")
    print(f"描述: {method_info['description']}")
    print(f"优点: {', '.join(method_info['advantages'])}")
    print(f"缺点: {', '.join(method_info['disadvantages'])}")

    print("LwF持续学习器测试完成！")
