"""
CNN模型定义，用于MNIST数字分类
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Optional


class SimpleCNN(nn.Module):
    """
    简单的CNN模型，适用于MNIST数据集
    """
    
    def __init__(self, num_classes: int = 10, dropout_rate: float = 0.5):
        """
        初始化CNN模型
        
        Args:
            num_classes: 分类类别数
            dropout_rate: Dropout比率
        """
        super(SimpleCNN, self).__init__()
        
        self.num_classes = num_classes
        
        # 卷积层
        self.conv1 = nn.Conv2d(1, 32, kernel_size=3, stride=1, padding=1)
        self.conv2 = nn.Conv2d(32, 64, kernel_size=3, stride=1, padding=1)
        self.conv3 = nn.Conv2d(64, 128, kernel_size=3, stride=1, padding=1)
        
        # 批归一化层
        self.bn1 = nn.BatchNorm2d(32)
        self.bn2 = nn.BatchNorm2d(64)
        self.bn3 = nn.BatchNorm2d(128)
        
        # 池化层
        self.pool = nn.MaxPool2d(kernel_size=2, stride=2)
        
        # 全连接层
        self.fc1 = nn.Linear(128 * 3 * 3, 256)  # 28x28 -> 14x14 -> 7x7 -> 3x3 (after padding)
        self.fc2 = nn.Linear(256, 128)
        self.fc3 = nn.Linear(128, num_classes)
        
        # Dropout层
        self.dropout = nn.Dropout(dropout_rate)
        
        # 初始化权重
        self._initialize_weights()
    
    def _initialize_weights(self):
        """初始化模型权重"""
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm2d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Linear):
                nn.init.normal_(m.weight, 0, 0.01)
                nn.init.constant_(m.bias, 0)
    
    def forward(self, x: torch.Tensor, return_features: bool = False) -> torch.Tensor:
        """
        前向传播
        
        Args:
            x: 输入张量 [batch_size, 1, 28, 28]
            return_features: 是否返回特征向量
            
        Returns:
            output: 输出张量 [batch_size, num_classes]
            features: 特征向量 [batch_size, 128] (如果return_features=True)
        """
        # 卷积层1
        x = self.conv1(x)
        x = self.bn1(x)
        x = F.relu(x)
        x = self.pool(x)  # 28x28 -> 14x14
        
        # 卷积层2
        x = self.conv2(x)
        x = self.bn2(x)
        x = F.relu(x)
        x = self.pool(x)  # 14x14 -> 7x7
        
        # 卷积层3
        x = self.conv3(x)
        x = self.bn3(x)
        x = F.relu(x)
        x = self.pool(x)  # 7x7 -> 3x3
        
        # 展平
        x = x.view(x.size(0), -1)
        
        # 全连接层1
        x = self.fc1(x)
        x = F.relu(x)
        x = self.dropout(x)
        
        # 全连接层2
        features = self.fc2(x)
        features = F.relu(features)
        features = self.dropout(features)
        
        # 输出层
        output = self.fc3(features)
        
        if return_features:
            return output, features
        return output
    
    def get_feature_extractor(self) -> nn.Module:
        """
        获取特征提取器（不包含最后的分类层）
        
        Returns:
            feature_extractor: 特征提取器模型
        """
        class FeatureExtractor(nn.Module):
            def __init__(self, original_model):
                super(FeatureExtractor, self).__init__()
                self.features = nn.Sequential(*list(original_model.children())[:-1])
            
            def forward(self, x):
                return self.features(x)
        
        return FeatureExtractor(self)
    
    def freeze_feature_layers(self):
        """冻结特征提取层的参数"""
        for param in self.conv1.parameters():
            param.requires_grad = False
        for param in self.conv2.parameters():
            param.requires_grad = False
        for param in self.conv3.parameters():
            param.requires_grad = False
        for param in self.bn1.parameters():
            param.requires_grad = False
        for param in self.bn2.parameters():
            param.requires_grad = False
        for param in self.bn3.parameters():
            param.requires_grad = False
        for param in self.fc1.parameters():
            param.requires_grad = False
        for param in self.fc2.parameters():
            param.requires_grad = False
    
    def unfreeze_all_layers(self):
        """解冻所有层的参数"""
        for param in self.parameters():
            param.requires_grad = True
    
    def get_classifier_parameters(self):
        """获取分类器层的参数"""
        return self.fc3.parameters()
    
    def get_feature_parameters(self):
        """获取特征提取层的参数"""
        feature_params = []
        for name, param in self.named_parameters():
            if not name.startswith('fc3'):
                feature_params.append(param)
        return feature_params


class AdaptiveCNN(SimpleCNN):
    """
    自适应CNN模型，支持动态调整输出类别数
    """
    
    def __init__(self, initial_classes: int = 5, max_classes: int = 10, dropout_rate: float = 0.5):
        """
        初始化自适应CNN模型
        
        Args:
            initial_classes: 初始类别数
            max_classes: 最大类别数
            dropout_rate: Dropout比率
        """
        super(AdaptiveCNN, self).__init__(num_classes=max_classes, dropout_rate=dropout_rate)
        
        self.initial_classes = initial_classes
        self.max_classes = max_classes
        self.current_classes = initial_classes
        
        # 重新定义输出层以支持动态扩展
        self.fc3 = nn.Linear(128, max_classes)
        
        # 初始化时只激活部分输出
        self._mask_unused_outputs()
    
    def _mask_unused_outputs(self):
        """屏蔽未使用的输出节点"""
        with torch.no_grad():
            if self.current_classes < self.max_classes:
                # 将未使用的输出权重设为很小的值
                self.fc3.weight.data[self.current_classes:] *= 0.01
                self.fc3.bias.data[self.current_classes:] *= 0.01
    
    def expand_classes(self, new_classes: int):
        """
        扩展模型以支持新的类别
        
        Args:
            new_classes: 新增的类别数
        """
        old_classes = self.current_classes
        self.current_classes = min(old_classes + new_classes, self.max_classes)
        
        print(f"模型类别数从 {old_classes} 扩展到 {self.current_classes}")
        
        # 重新初始化新增类别的输出权重
        with torch.no_grad():
            if self.current_classes > old_classes:
                nn.init.normal_(self.fc3.weight.data[old_classes:self.current_classes], 0, 0.01)
                nn.init.constant_(self.fc3.bias.data[old_classes:self.current_classes], 0)
    
    def forward(self, x: torch.Tensor, return_features: bool = False) -> torch.Tensor:
        """
        前向传播，只返回当前激活类别的输出
        
        Args:
            x: 输入张量
            return_features: 是否返回特征向量
            
        Returns:
            output: 输出张量，只包含当前激活的类别
        """
        if return_features:
            full_output, features = super().forward(x, return_features=True)
            output = full_output[:, :self.current_classes]
            return output, features
        else:
            full_output = super().forward(x, return_features=False)
            output = full_output[:, :self.current_classes]
            return output


def create_model(model_type: str = "simple", **kwargs) -> nn.Module:
    """
    创建模型的工厂函数
    
    Args:
        model_type: 模型类型 ("simple" 或 "adaptive")
        **kwargs: 模型参数
        
    Returns:
        model: 创建的模型
    """
    if model_type == "simple":
        return SimpleCNN(**kwargs)
    elif model_type == "adaptive":
        return AdaptiveCNN(**kwargs)
    else:
        raise ValueError(f"不支持的模型类型: {model_type}")


if __name__ == "__main__":
    # 测试模型
    print("测试CNN模型...")
    
    # 创建简单CNN模型
    model = SimpleCNN(num_classes=10)
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters())}")
    
    # 测试前向传播
    x = torch.randn(4, 1, 28, 28)  # 批次大小为4的随机输入
    output = model(x)
    print(f"输入形状: {x.shape}")
    print(f"输出形状: {output.shape}")
    
    # 测试特征提取
    output, features = model(x, return_features=True)
    print(f"特征形状: {features.shape}")
    
    # 测试自适应模型
    print("\n测试自适应CNN模型...")
    adaptive_model = AdaptiveCNN(initial_classes=5, max_classes=10)
    output = adaptive_model(x)
    print(f"初始输出形状: {output.shape}")
    
    # 扩展类别
    adaptive_model.expand_classes(5)
    output = adaptive_model(x)
    print(f"扩展后输出形状: {output.shape}")
    
    print("模型测试完成！")
