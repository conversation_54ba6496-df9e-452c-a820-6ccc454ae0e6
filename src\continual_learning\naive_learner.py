"""
朴素持续学习方法 - 直接在新数据上继续训练，展示灾难性遗忘现象
"""

import torch
import torch.nn as nn
from torch.utils.data import DataLoader
from typing import Dict
from .base_learner import BaseContinualLearner


class NaiveContinualLearner(BaseContinualLearner):
    """
    朴素持续学习器
    直接在新任务数据上继续训练，不采用任何防遗忘策略
    """
    
    def __init__(self, model: nn.Module, device: torch.device, learning_rate: float = 0.001):
        """
        初始化朴素持续学习器
        
        Args:
            model: 神经网络模型
            device: 计算设备
            learning_rate: 学习率
        """
        super().__init__(model, device, learning_rate)
        self.method_name = "Naive"
        
        print(f"初始化朴素持续学习器 (学习率: {learning_rate})")
    
    def before_task(self, task_id: int, train_loader: DataLoader):
        """
        任务开始前的准备工作
        
        Args:
            task_id: 任务ID
            train_loader: 训练数据加载器
        """
        print(f"\n=== 开始任务 {task_id} (朴素方法) ===")
        
        # 记录任务信息
        task_info = {
            'task_id': task_id,
            'method': self.method_name,
            'num_samples': len(train_loader.dataset)
        }
        
        # 获取任务中的类别信息
        classes_in_task = set()
        for _, targets in train_loader:
            classes_in_task.update(targets.numpy().tolist())
        
        task_info['classes'] = sorted(list(classes_in_task))
        self.task_history.append(task_info)
        
        print(f"任务 {task_id} 包含类别: {task_info['classes']}")
        print(f"训练样本数量: {task_info['num_samples']}")
        
        # 朴素方法不需要特殊的准备工作
        # 直接使用当前模型状态继续训练
    
    def after_task(self, task_id: int):
        """
        任务完成后的处理工作
        
        Args:
            task_id: 任务ID
        """
        print(f"任务 {task_id} 完成 (朴素方法)")
        self.current_task = task_id
        
        # 朴素方法不需要特殊的后处理工作
    
    def learn_task(self, train_loader: DataLoader, task_id: int, epochs: int = 10) -> Dict:
        """
        学习新任务
        
        Args:
            train_loader: 训练数据加载器
            task_id: 任务ID
            epochs: 训练轮数
            
        Returns:
            training_stats: 训练统计信息
        """
        # 任务开始前的准备
        self.before_task(task_id, train_loader)
        
        # 训练统计
        training_stats = {
            'task_id': task_id,
            'method': self.method_name,
            'epochs': epochs,
            'epoch_stats': []
        }
        
        print(f"开始训练任务 {task_id}，共 {epochs} 个epoch")
        
        # 训练循环
        for epoch in range(epochs):
            epoch_stats = self.train_epoch(train_loader, epoch)
            training_stats['epoch_stats'].append(epoch_stats)
            
            # 打印训练进度
            if (epoch + 1) % 5 == 0 or epoch == 0:
                print(f"Epoch {epoch+1}/{epochs}: "
                      f"Loss: {epoch_stats['loss']:.4f}, "
                      f"Accuracy: {epoch_stats['accuracy']:.2f}%")
        
        # 任务完成后的处理
        self.after_task(task_id)
        
        # 记录最终训练统计
        final_stats = training_stats['epoch_stats'][-1]
        training_stats['final_loss'] = final_stats['loss']
        training_stats['final_accuracy'] = final_stats['accuracy']
        
        print(f"任务 {task_id} 训练完成:")
        print(f"  最终损失: {training_stats['final_loss']:.4f}")
        print(f"  最终准确率: {training_stats['final_accuracy']:.2f}%")
        
        return training_stats
    
    def train_epoch(self, train_loader: DataLoader, epoch: int) -> Dict[str, float]:
        """
        训练一个epoch（朴素方法的标准训练）
        
        Args:
            train_loader: 训练数据加载器
            epoch: 当前epoch
            
        Returns:
            epoch_stats: epoch统计信息
        """
        self.model.train()
        total_loss = 0.0
        correct = 0
        total = 0
        
        for batch_idx, (data, target) in enumerate(train_loader):
            data, target = data.to(self.device), target.to(self.device)
            
            # 前向传播
            self.optimizer.zero_grad()
            output = self.model(data)
            
            # 计算损失（标准交叉熵损失）
            loss = self.criterion(output, target)
            
            # 反向传播
            loss.backward()
            self.optimizer.step()
            
            # 统计
            total_loss += loss.item()
            pred = output.argmax(dim=1)
            correct += pred.eq(target).sum().item()
            total += target.size(0)
        
        avg_loss = total_loss / len(train_loader)
        accuracy = 100.0 * correct / total
        
        return {
            'loss': avg_loss,
            'accuracy': accuracy,
            'correct': correct,
            'total': total
        }
    
    def get_method_info(self) -> Dict:
        """
        获取方法信息
        
        Returns:
            method_info: 方法信息字典
        """
        return {
            'name': self.method_name,
            'description': '朴素持续学习方法，直接在新数据上继续训练',
            'advantages': [
                '实现简单',
                '训练速度快',
                '内存开销小'
            ],
            'disadvantages': [
                '严重的灾难性遗忘',
                '无法保持旧知识',
                '性能随任务数量下降'
            ],
            'parameters': {
                'learning_rate': self.learning_rate,
                'optimizer': type(self.optimizer).__name__,
                'criterion': type(self.criterion).__name__
            }
        }
    
    def analyze_forgetting(self, test_loaders: Dict[int, DataLoader]) -> Dict:
        """
        分析遗忘情况
        
        Args:
            test_loaders: 各任务的测试数据加载器字典
            
        Returns:
            forgetting_analysis: 遗忘分析结果
        """
        print(f"\n=== 分析遗忘情况 (朴素方法) ===")
        
        forgetting_analysis = {
            'method': self.method_name,
            'task_performances': {},
            'forgetting_metrics': {}
        }
        
        # 评估每个任务的性能
        for task_id, test_loader in test_loaders.items():
            print(f"评估任务 {task_id} 性能...")
            performance = self.evaluate(test_loader)
            forgetting_analysis['task_performances'][task_id] = performance
            
            print(f"任务 {task_id}: 准确率 {performance['accuracy']:.2f}%")
        
        # 计算遗忘指标
        if len(forgetting_analysis['task_performances']) > 1:
            task_ids = sorted(forgetting_analysis['task_performances'].keys())
            
            # 计算平均遗忘
            accuracies = [forgetting_analysis['task_performances'][tid]['accuracy'] 
                         for tid in task_ids]
            
            forgetting_analysis['forgetting_metrics'] = {
                'average_accuracy': sum(accuracies) / len(accuracies),
                'accuracy_drop': accuracies[0] - accuracies[-1] if len(accuracies) > 1 else 0,
                'task_accuracies': dict(zip(task_ids, accuracies))
            }
            
            print(f"平均准确率: {forgetting_analysis['forgetting_metrics']['average_accuracy']:.2f}%")
            print(f"准确率下降: {forgetting_analysis['forgetting_metrics']['accuracy_drop']:.2f}%")
        
        return forgetting_analysis


if __name__ == "__main__":
    # 测试朴素持续学习器
    print("测试朴素持续学习器...")
    
    # 创建简单的测试模型
    from ..models.cnn_model import SimpleCNN
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = SimpleCNN(num_classes=10)
    
    # 创建朴素学习器
    learner = NaiveContinualLearner(model, device, learning_rate=0.001)
    
    # 打印方法信息
    method_info = learner.get_method_info()
    print(f"\n方法: {method_info['name']}")
    print(f"描述: {method_info['description']}")
    print(f"优点: {', '.join(method_info['advantages'])}")
    print(f"缺点: {', '.join(method_info['disadvantages'])}")
    
    print("朴素持续学习器测试完成！")
