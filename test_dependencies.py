"""
测试依赖安装情况
"""

def test_imports():
    """测试所有必需的包是否可以导入"""
    packages = {
        'torch': 'PyTorch',
        'torchvision': 'TorchVision', 
        'numpy': 'NumPy',
        'matplotlib': '<PERSON><PERSON>lotlib',
        'seaborn': 'Seaborn',
        'sklearn': 'Scikit-learn',
        'tqdm': 'TQDM',
        'pandas': 'Pandas'
    }
    
    print("检查依赖包...")
    print("=" * 40)
    
    success_count = 0
    total_count = len(packages)
    
    for package, name in packages.items():
        try:
            module = __import__(package)
            version = getattr(module, '__version__', 'Unknown')
            print(f"✓ {name:12} - 版本: {version}")
            success_count += 1
        except ImportError as e:
            print(f"✗ {name:12} - 未安装或导入失败: {str(e)}")
    
    print("=" * 40)
    print(f"成功: {success_count}/{total_count}")
    
    if success_count == total_count:
        print("✓ 所有依赖都已正确安装！")
        return True
    else:
        print("✗ 部分依赖缺失，请安装缺失的包")
        return False

if __name__ == "__main__":
    test_imports()
