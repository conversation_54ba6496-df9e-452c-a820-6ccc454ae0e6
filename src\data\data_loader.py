"""
MNIST数据加载器，支持类增量学习的数据划分
"""

import torch
import torchvision
import torchvision.transforms as transforms
from torch.utils.data import DataLoader, Subset
import numpy as np
from typing import List, Tuple, Dict


class MNISTIncrementalDataLoader:
    """
    MNIST类增量学习数据加载器
    支持将10个类别分批次加载，模拟持续学习场景
    """
    
    def __init__(self, data_dir: str = './data', batch_size: int = 128):
        """
        初始化数据加载器
        
        Args:
            data_dir: 数据存储目录
            batch_size: 批次大小
        """
        self.data_dir = data_dir
        self.batch_size = batch_size
        
        # 数据预处理
        self.transform = transforms.Compose([
            transforms.ToTensor(),
            transforms.Normalize((0.1307,), (0.3081,))  # MNIST标准化参数
        ])
        
        # 加载完整的MNIST数据集
        self.train_dataset = torchvision.datasets.MNIST(
            root=data_dir, train=True, download=True, transform=self.transform
        )
        self.test_dataset = torchvision.datasets.MNIST(
            root=data_dir, train=False, download=True, transform=self.transform
        )
        
        # 获取数据和标签
        self.train_data = self.train_dataset.data
        self.train_targets = self.train_dataset.targets
        self.test_data = self.test_dataset.data
        self.test_targets = self.test_dataset.targets
        
        print(f"MNIST数据集加载完成:")
        print(f"训练集大小: {len(self.train_dataset)}")
        print(f"测试集大小: {len(self.test_dataset)}")
    
    def get_task_data(self, task_classes: List[int]) -> Tuple[DataLoader, DataLoader]:
        """
        获取指定类别的训练和测试数据加载器
        
        Args:
            task_classes: 当前任务包含的类别列表
            
        Returns:
            train_loader: 训练数据加载器
            test_loader: 测试数据加载器
        """
        # 获取训练集中指定类别的索引
        train_indices = []
        for class_id in task_classes:
            class_indices = (self.train_targets == class_id).nonzero(as_tuple=True)[0]
            train_indices.extend(class_indices.tolist())
        
        # 获取测试集中指定类别的索引
        test_indices = []
        for class_id in task_classes:
            class_indices = (self.test_targets == class_id).nonzero(as_tuple=True)[0]
            test_indices.extend(class_indices.tolist())
        
        # 创建子数据集
        train_subset = Subset(self.train_dataset, train_indices)
        test_subset = Subset(self.test_dataset, test_indices)
        
        # 创建数据加载器
        train_loader = DataLoader(
            train_subset, batch_size=self.batch_size, shuffle=True, num_workers=0
        )
        test_loader = DataLoader(
            test_subset, batch_size=self.batch_size, shuffle=False, num_workers=0
        )
        
        print(f"任务类别 {task_classes}: 训练样本 {len(train_subset)}, 测试样本 {len(test_subset)}")
        
        return train_loader, test_loader
    
    def get_full_test_loader(self) -> DataLoader:
        """
        获取完整测试集的数据加载器，用于评估整体性能
        
        Returns:
            test_loader: 完整测试集数据加载器
        """
        return DataLoader(
            self.test_dataset, batch_size=self.batch_size, shuffle=False, num_workers=0
        )
    
    def get_class_distribution(self) -> Dict[int, int]:
        """
        获取数据集中各类别的样本数量分布
        
        Returns:
            class_counts: 各类别样本数量字典
        """
        train_counts = {}
        test_counts = {}
        
        for class_id in range(10):
            train_count = (self.train_targets == class_id).sum().item()
            test_count = (self.test_targets == class_id).sum().item()
            train_counts[class_id] = train_count
            test_counts[class_id] = test_count
        
        print("训练集类别分布:", train_counts)
        print("测试集类别分布:", test_counts)
        
        return {"train": train_counts, "test": test_counts}


class IncrementalTaskManager:
    """
    增量任务管理器，定义任务划分策略
    """
    
    def __init__(self):
        """初始化任务管理器"""
        self.tasks = []
    
    def create_split_tasks(self, split_strategy: str = "5-5") -> List[List[int]]:
        """
        创建任务划分
        
        Args:
            split_strategy: 划分策略，如"5-5"表示两个任务各5个类别
            
        Returns:
            tasks: 任务列表，每个任务包含一组类别
        """
        if split_strategy == "5-5":
            self.tasks = [
                [0, 1, 2, 3, 4],  # 第一个任务：数字0-4
                [5, 6, 7, 8, 9]   # 第二个任务：数字5-9
            ]
        elif split_strategy == "2-2-2-2-2":
            self.tasks = [
                [0, 1], [2, 3], [4, 5], [6, 7], [8, 9]
            ]
        elif split_strategy == "1-1-1-1-1-1-1-1-1-1":
            self.tasks = [[i] for i in range(10)]
        else:
            raise ValueError(f"不支持的划分策略: {split_strategy}")
        
        print(f"任务划分策略 '{split_strategy}':")
        for i, task in enumerate(self.tasks):
            print(f"  任务 {i+1}: 类别 {task}")
        
        return self.tasks
    
    def get_cumulative_classes(self, task_id: int) -> List[int]:
        """
        获取到指定任务为止的累积类别
        
        Args:
            task_id: 任务ID（从0开始）
            
        Returns:
            cumulative_classes: 累积类别列表
        """
        if task_id >= len(self.tasks):
            raise ValueError(f"任务ID {task_id} 超出范围")
        
        cumulative_classes = []
        for i in range(task_id + 1):
            cumulative_classes.extend(self.tasks[i])
        
        return sorted(cumulative_classes)


if __name__ == "__main__":
    # 测试数据加载器
    print("测试MNIST增量数据加载器...")
    
    # 创建数据加载器
    data_loader = MNISTIncrementalDataLoader()
    
    # 查看类别分布
    data_loader.get_class_distribution()
    
    # 创建任务管理器
    task_manager = IncrementalTaskManager()
    tasks = task_manager.create_split_tasks("5-5")
    
    # 测试第一个任务的数据加载
    print("\n测试第一个任务数据加载...")
    train_loader, test_loader = data_loader.get_task_data(tasks[0])
    
    # 查看一个批次的数据
    for batch_idx, (data, target) in enumerate(train_loader):
        print(f"批次 {batch_idx}: 数据形状 {data.shape}, 标签形状 {target.shape}")
        print(f"标签范围: {target.min().item()} - {target.max().item()}")
        if batch_idx == 0:
            break
    
    print("数据加载器测试完成！")
