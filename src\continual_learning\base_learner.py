"""
基础持续学习器抽象类
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Tuple
import copy


class BaseContinualLearner(ABC):
    """
    持续学习器基类，定义了持续学习的基本接口
    """
    
    def __init__(self, model: nn.Module, device: torch.device, learning_rate: float = 0.001):
        """
        初始化基础持续学习器
        
        Args:
            model: 神经网络模型
            device: 计算设备
            learning_rate: 学习率
        """
        self.model = model.to(device)
        self.device = device
        self.learning_rate = learning_rate
        
        # 优化器
        self.optimizer = optim.Adam(self.model.parameters(), lr=learning_rate)
        
        # 损失函数
        self.criterion = nn.CrossEntropyLoss()
        
        # 任务历史
        self.task_history = []
        self.current_task = 0
        
        # 性能记录
        self.performance_history = []
        
    @abstractmethod
    def learn_task(self, train_loader: DataLoader, task_id: int, epochs: int = 10) -> Dict:
        """
        学习新任务
        
        Args:
            train_loader: 训练数据加载器
            task_id: 任务ID
            epochs: 训练轮数
            
        Returns:
            training_stats: 训练统计信息
        """
        pass
    
    @abstractmethod
    def before_task(self, task_id: int, train_loader: DataLoader):
        """
        任务开始前的准备工作
        
        Args:
            task_id: 任务ID
            train_loader: 训练数据加载器
        """
        pass
    
    @abstractmethod
    def after_task(self, task_id: int):
        """
        任务完成后的处理工作
        
        Args:
            task_id: 任务ID
        """
        pass
    
    def evaluate(self, test_loader: DataLoader) -> Dict[str, float]:
        """
        评估模型性能
        
        Args:
            test_loader: 测试数据加载器
            
        Returns:
            metrics: 评估指标字典
        """
        self.model.eval()
        total_loss = 0.0
        correct = 0
        total = 0
        
        class_correct = {}
        class_total = {}
        
        with torch.no_grad():
            for data, target in test_loader:
                data, target = data.to(self.device), target.to(self.device)
                
                output = self.model(data)
                loss = self.criterion(output, target)
                total_loss += loss.item()
                
                pred = output.argmax(dim=1)
                correct += pred.eq(target).sum().item()
                total += target.size(0)
                
                # 计算每个类别的准确率
                for i in range(len(target)):
                    label = target[i].item()
                    if label not in class_correct:
                        class_correct[label] = 0
                        class_total[label] = 0
                    
                    class_total[label] += 1
                    if pred[i] == label:
                        class_correct[label] += 1
        
        # 计算整体指标
        avg_loss = total_loss / len(test_loader)
        accuracy = 100.0 * correct / total
        
        # 计算每个类别的准确率
        class_accuracies = {}
        for class_id in class_total:
            if class_total[class_id] > 0:
                class_accuracies[class_id] = 100.0 * class_correct[class_id] / class_total[class_id]
        
        metrics = {
            'loss': avg_loss,
            'accuracy': accuracy,
            'correct': correct,
            'total': total,
            'class_accuracies': class_accuracies
        }
        
        return metrics
    
    def save_model(self, filepath: str):
        """
        保存模型
        
        Args:
            filepath: 保存路径
        """
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'task_history': self.task_history,
            'current_task': self.current_task,
            'performance_history': self.performance_history
        }, filepath)
    
    def load_model(self, filepath: str):
        """
        加载模型
        
        Args:
            filepath: 模型文件路径
        """
        checkpoint = torch.load(filepath, map_location=self.device)
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        self.task_history = checkpoint.get('task_history', [])
        self.current_task = checkpoint.get('current_task', 0)
        self.performance_history = checkpoint.get('performance_history', [])
    
    def get_model_copy(self) -> nn.Module:
        """
        获取模型的深拷贝
        
        Returns:
            model_copy: 模型副本
        """
        return copy.deepcopy(self.model)
    
    def compute_forgetting(self, current_performance: Dict, previous_performance: Dict) -> Dict[str, float]:
        """
        计算遗忘程度
        
        Args:
            current_performance: 当前性能
            previous_performance: 之前性能
            
        Returns:
            forgetting_metrics: 遗忘指标
        """
        forgetting_metrics = {}
        
        # 整体遗忘
        if 'accuracy' in current_performance and 'accuracy' in previous_performance:
            forgetting_metrics['overall_forgetting'] = previous_performance['accuracy'] - current_performance['accuracy']
        
        # 各类别遗忘
        if 'class_accuracies' in current_performance and 'class_accuracies' in previous_performance:
            class_forgetting = {}
            for class_id in previous_performance['class_accuracies']:
                if class_id in current_performance['class_accuracies']:
                    class_forgetting[class_id] = (
                        previous_performance['class_accuracies'][class_id] - 
                        current_performance['class_accuracies'][class_id]
                    )
            forgetting_metrics['class_forgetting'] = class_forgetting
        
        return forgetting_metrics
    
    def train_epoch(self, train_loader: DataLoader, epoch: int) -> Dict[str, float]:
        """
        训练一个epoch
        
        Args:
            train_loader: 训练数据加载器
            epoch: 当前epoch
            
        Returns:
            epoch_stats: epoch统计信息
        """
        self.model.train()
        total_loss = 0.0
        correct = 0
        total = 0
        
        for batch_idx, (data, target) in enumerate(train_loader):
            data, target = data.to(self.device), target.to(self.device)
            
            self.optimizer.zero_grad()
            output = self.model(data)
            loss = self.criterion(output, target)
            loss.backward()
            self.optimizer.step()
            
            total_loss += loss.item()
            pred = output.argmax(dim=1)
            correct += pred.eq(target).sum().item()
            total += target.size(0)
        
        avg_loss = total_loss / len(train_loader)
        accuracy = 100.0 * correct / total
        
        return {
            'loss': avg_loss,
            'accuracy': accuracy,
            'correct': correct,
            'total': total
        }


class ContinualLearningMetrics:
    """
    持续学习评估指标计算器
    """
    
    @staticmethod
    def compute_average_accuracy(performance_history: List[Dict]) -> float:
        """
        计算平均准确率
        
        Args:
            performance_history: 性能历史记录
            
        Returns:
            average_accuracy: 平均准确率
        """
        if not performance_history:
            return 0.0
        
        total_accuracy = sum(perf['accuracy'] for perf in performance_history)
        return total_accuracy / len(performance_history)
    
    @staticmethod
    def compute_backward_transfer(performance_history: List[Dict]) -> float:
        """
        计算后向迁移（遗忘程度）
        
        Args:
            performance_history: 性能历史记录
            
        Returns:
            backward_transfer: 后向迁移指标
        """
        if len(performance_history) < 2:
            return 0.0
        
        # 计算每个任务在学习后续任务时的性能下降
        backward_transfers = []
        for i in range(len(performance_history) - 1):
            initial_acc = performance_history[i]['accuracy']
            final_acc = performance_history[-1]['accuracy']  # 最终性能
            backward_transfers.append(final_acc - initial_acc)
        
        return sum(backward_transfers) / len(backward_transfers)
    
    @staticmethod
    def compute_forward_transfer(performance_history: List[Dict]) -> float:
        """
        计算前向迁移（知识迁移能力）
        
        Args:
            performance_history: 性能历史记录
            
        Returns:
            forward_transfer: 前向迁移指标
        """
        # 这里简化实现，实际应该比较有无预训练的性能差异
        if len(performance_history) < 2:
            return 0.0
        
        # 假设第一个任务的性能作为基准
        baseline = performance_history[0]['accuracy']
        improvements = []
        
        for i in range(1, len(performance_history)):
            improvement = performance_history[i]['accuracy'] - baseline
            improvements.append(improvement)
        
        return sum(improvements) / len(improvements) if improvements else 0.0
