"""
快速实验脚本 - 专门解决超时问题
"""

import subprocess
import sys
import time

def run_minimal_experiment():
    """运行最小化实验，确保不会超时"""
    print("运行最小化实验（确保成功完成）")
    print("=" * 50)
    
    try:
        # 使用最快的配置
        cmd = [
            sys.executable, 'main.py',
            '--config', 'quick',
            '--methods', 'naive', 'lwf',  # 只用两种方法
            '--verbose'
        ]
        
        print("命令:", ' '.join(cmd))
        print("预计时间: 3-5分钟")
        print("开始执行...")
        print("-" * 30)
        
        start_time = time.time()
        
        # 使用实时输出
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        # 显示实时输出和进度
        line_count = 0
        for line in process.stdout:
            line_count += 1
            elapsed = time.time() - start_time
            print(f"[{elapsed:.0f}s] {line.rstrip()}")
            
            # 每50行显示一次进度
            if line_count % 50 == 0:
                print(f">>> 已运行 {elapsed:.1f}秒，处理了 {line_count} 行输出")
        
        # 等待完成
        return_code = process.wait(timeout=600)  # 10分钟超时
        
        elapsed_total = time.time() - start_time
        
        if return_code == 0:
            print("-" * 30)
            print(f"✓ 实验成功完成！总用时: {elapsed_total:.1f}秒")
            return True
        else:
            print("-" * 30)
            print(f"✗ 实验失败，返回码: {return_code}")
            return False
            
    except subprocess.TimeoutExpired:
        process.kill()
        print("-" * 30)
        print("✗ 实验超时（超过10分钟）")
        print("建议检查:")
        print("1. 是否有GPU可用")
        print("2. 数据集是否正确下载")
        print("3. 系统资源是否充足")
        return False
        
    except Exception as e:
        print(f"✗ 实验出错: {str(e)}")
        return False


def run_single_method_test():
    """运行单一方法测试"""
    print("\n运行单一方法测试（最快）")
    print("=" * 50)
    
    methods = ['naive', 'lwf', 'ewc']
    
    for method in methods:
        print(f"\n测试方法: {method}")
        print("-" * 20)
        
        try:
            cmd = [
                sys.executable, 'main.py',
                '--config', 'quick',
                '--methods', method,
                '--verbose'
            ]
            
            start_time = time.time()
            result = subprocess.run(cmd, timeout=300, capture_output=True, text=True)
            elapsed = time.time() - start_time
            
            if result.returncode == 0:
                print(f"✓ {method} 成功！用时: {elapsed:.1f}秒")
            else:
                print(f"✗ {method} 失败！")
                print("错误输出:")
                print(result.stderr[:500])  # 只显示前500字符
                
        except subprocess.TimeoutExpired:
            print(f"✗ {method} 超时（超过5分钟）")
        except Exception as e:
            print(f"✗ {method} 出错: {str(e)}")


def check_system_resources():
    """检查系统资源"""
    print("检查系统资源...")
    print("-" * 30)
    
    try:
        import psutil
        
        # CPU信息
        cpu_percent = psutil.cpu_percent(interval=1)
        cpu_count = psutil.cpu_count()
        print(f"CPU: {cpu_count}核, 当前使用率: {cpu_percent}%")
        
        # 内存信息
        memory = psutil.virtual_memory()
        memory_gb = memory.total / (1024**3)
        memory_percent = memory.percent
        print(f"内存: {memory_gb:.1f}GB, 当前使用率: {memory_percent}%")
        
        # 磁盘信息
        disk = psutil.disk_usage('.')
        disk_gb = disk.free / (1024**3)
        print(f"磁盘可用空间: {disk_gb:.1f}GB")
        
        # 建议
        if memory_percent > 80:
            print("⚠️  内存使用率较高，可能影响训练速度")
        if cpu_percent > 80:
            print("⚠️  CPU使用率较高，可能影响训练速度")
        if disk_gb < 1:
            print("⚠️  磁盘空间不足，可能影响结果保存")
            
    except ImportError:
        print("未安装psutil，无法检查系统资源")
        print("可以运行: pip install psutil")
    except Exception as e:
        print(f"检查系统资源时出错: {str(e)}")


def main():
    """主函数"""
    print("快速实验脚本 - 解决超时问题")
    print("=" * 50)
    
    # 检查系统资源
    check_system_resources()
    
    print("\n请选择实验类型:")
    print("1. 最小化实验 (naive + lwf, 约3-5分钟)")
    print("2. 单一方法测试 (逐个测试每种方法)")
    print("3. 完整快速实验 (所有方法, 约5-8分钟)")
    print("0. 退出")
    
    while True:
        choice = input("\n请选择 (0-3): ").strip()
        
        if choice == '0':
            print("退出")
            break
        elif choice == '1':
            run_minimal_experiment()
            break
        elif choice == '2':
            run_single_method_test()
            break
        elif choice == '3':
            print("运行完整快速实验...")
            success = run_minimal_experiment()
            if success:
                print("\n继续运行EWC方法...")
                try:
                    result = subprocess.run([
                        sys.executable, 'main.py',
                        '--config', 'quick',
                        '--methods', 'ewc',
                        '--verbose'
                    ], timeout=300)
                    
                    if result.returncode == 0:
                        print("✓ EWC方法也成功完成！")
                    else:
                        print("✗ EWC方法失败")
                except subprocess.TimeoutExpired:
                    print("✗ EWC方法超时")
            break
        else:
            print("无效选择，请重新输入")


if __name__ == "__main__":
    main()
