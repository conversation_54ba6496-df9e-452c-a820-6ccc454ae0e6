"""
Elastic Weight Consolidation (EWC) 持续学习方法
通过Fisher信息矩阵保护重要参数
"""

import torch
import torch.nn as nn
from torch.utils.data import DataLoader
from typing import Dict, List
import copy
from .base_learner import BaseContinualLearner


class EWCContinualLearner(BaseContinualLearner):
    """
    Elastic Weight Consolidation 持续学习器
    使用Fisher信息矩阵保护重要参数，防止灾难性遗忘
    """
    
    def __init__(self, model: nn.Module, device: torch.device, learning_rate: float = 0.001,
                 ewc_lambda: float = 1000.0, fisher_sample_size: int = 1000):
        """
        初始化EWC持续学习器
        
        Args:
            model: 神经网络模型
            device: 计算设备
            learning_rate: 学习率
            ewc_lambda: EWC正则化强度
            fisher_sample_size: 计算Fisher信息矩阵的样本数量
        """
        super().__init__(model, device, learning_rate)
        self.method_name = "EWC"
        
        # EWC特有参数
        self.ewc_lambda = ewc_lambda
        self.fisher_sample_size = fisher_sample_size
        
        # 存储重要参数和Fisher信息矩阵
        self.important_params = {}
        self.fisher_matrices = {}
        
        print(f"初始化EWC持续学习器:")
        print(f"  学习率: {learning_rate}")
        print(f"  EWC正则化强度: {ewc_lambda}")
        print(f"  Fisher样本数量: {fisher_sample_size}")
    
    def before_task(self, task_id: int, train_loader: DataLoader):
        """
        任务开始前的准备工作
        
        Args:
            task_id: 任务ID
            train_loader: 训练数据加载器
        """
        print(f"\n=== 开始任务 {task_id} (EWC方法) ===")
        
        # 记录任务信息
        task_info = {
            'task_id': task_id,
            'method': self.method_name,
            'num_samples': len(train_loader.dataset)
        }
        
        # 获取任务中的类别信息
        classes_in_task = set()
        for _, targets in train_loader:
            classes_in_task.update(targets.numpy().tolist())
        
        task_info['classes'] = sorted(list(classes_in_task))
        self.task_history.append(task_info)
        
        print(f"任务 {task_id} 包含类别: {task_info['classes']}")
        print(f"训练样本数量: {task_info['num_samples']}")
        
        # 如果不是第一个任务，计算Fisher信息矩阵
        if task_id > 0:
            print("计算Fisher信息矩阵...")
            self.compute_fisher_matrix(train_loader, task_id)
        else:
            print("第一个任务，无需计算Fisher信息矩阵")
    
    def after_task(self, task_id: int):
        """
        任务完成后的处理工作
        
        Args:
            task_id: 任务ID
        """
        print(f"任务 {task_id} 完成 (EWC方法)")
        
        # 保存当前任务的重要参数
        self.save_important_params(task_id)
        self.current_task = task_id
    
    def save_important_params(self, task_id: int):
        """
        保存当前任务的重要参数
        
        Args:
            task_id: 任务ID
        """
        important_params = {}
        for name, param in self.model.named_parameters():
            if param.requires_grad:
                important_params[name] = param.data.clone()
        
        self.important_params[task_id] = important_params
        print(f"保存任务 {task_id} 的重要参数")
    
    def compute_fisher_matrix(self, data_loader: DataLoader, task_id: int):
        """
        计算Fisher信息矩阵
        
        Args:
            data_loader: 数据加载器
            task_id: 任务ID
        """
        self.model.eval()
        
        # 初始化Fisher矩阵
        fisher_matrix = {}
        for name, param in self.model.named_parameters():
            if param.requires_grad:
                fisher_matrix[name] = torch.zeros_like(param.data)
        
        # 计算Fisher信息矩阵
        sample_count = 0
        for data, target in data_loader:
            if sample_count >= self.fisher_sample_size:
                break
                
            data, target = data.to(self.device), target.to(self.device)
            batch_size = data.size(0)
            
            # 前向传播
            output = self.model(data)
            
            # 对每个样本计算梯度
            for i in range(batch_size):
                if sample_count >= self.fisher_sample_size:
                    break
                
                self.model.zero_grad()
                
                # 计算单个样本的损失
                sample_output = output[i:i+1]
                sample_target = target[i:i+1]
                loss = self.criterion(sample_output, sample_target)
                
                # 反向传播
                loss.backward(retain_graph=True)
                
                # 累积梯度的平方
                for name, param in self.model.named_parameters():
                    if param.requires_grad and param.grad is not None:
                        fisher_matrix[name] += param.grad.data ** 2
                
                sample_count += 1
        
        # 归一化Fisher矩阵
        for name in fisher_matrix:
            fisher_matrix[name] /= sample_count
        
        self.fisher_matrices[task_id] = fisher_matrix
        print(f"Fisher信息矩阵计算完成 (使用 {sample_count} 个样本)")
    
    def ewc_penalty(self) -> torch.Tensor:
        """
        计算EWC正则化惩罚项
        
        Returns:
            penalty: EWC惩罚项
        """
        penalty = torch.tensor(0.0).to(self.device)
        
        for task_id in self.important_params:
            if task_id in self.fisher_matrices:
                for name, param in self.model.named_parameters():
                    if param.requires_grad and name in self.important_params[task_id]:
                        # 计算参数变化
                        param_diff = param - self.important_params[task_id][name]
                        
                        # 加权惩罚
                        if name in self.fisher_matrices[task_id]:
                            fisher_weight = self.fisher_matrices[task_id][name]
                            penalty += (fisher_weight * param_diff ** 2).sum()
        
        return penalty
    
    def learn_task(self, train_loader: DataLoader, task_id: int, epochs: int = 10) -> Dict:
        """
        学习新任务
        
        Args:
            train_loader: 训练数据加载器
            task_id: 任务ID
            epochs: 训练轮数
            
        Returns:
            training_stats: 训练统计信息
        """
        # 任务开始前的准备
        self.before_task(task_id, train_loader)
        
        # 训练统计
        training_stats = {
            'task_id': task_id,
            'method': self.method_name,
            'epochs': epochs,
            'epoch_stats': []
        }
        
        print(f"开始训练任务 {task_id}，共 {epochs} 个epoch")
        
        # 训练循环
        for epoch in range(epochs):
            epoch_stats = self.train_epoch_with_ewc(train_loader, epoch)
            training_stats['epoch_stats'].append(epoch_stats)
            
            # 打印训练进度
            if (epoch + 1) % 5 == 0 or epoch == 0:
                if len(self.important_params) > 0:
                    print(f"Epoch {epoch+1}/{epochs}: "
                          f"Total Loss: {epoch_stats['total_loss']:.4f}, "
                          f"CE Loss: {epoch_stats['ce_loss']:.4f}, "
                          f"EWC Loss: {epoch_stats['ewc_loss']:.4f}, "
                          f"Accuracy: {epoch_stats['accuracy']:.2f}%")
                else:
                    print(f"Epoch {epoch+1}/{epochs}: "
                          f"Loss: {epoch_stats['total_loss']:.4f}, "
                          f"Accuracy: {epoch_stats['accuracy']:.2f}%")
        
        # 任务完成后的处理
        self.after_task(task_id)
        
        # 记录最终训练统计
        final_stats = training_stats['epoch_stats'][-1]
        training_stats['final_loss'] = final_stats['total_loss']
        training_stats['final_accuracy'] = final_stats['accuracy']
        
        print(f"任务 {task_id} 训练完成:")
        print(f"  最终损失: {training_stats['final_loss']:.4f}")
        print(f"  最终准确率: {training_stats['final_accuracy']:.2f}%")
        
        return training_stats

    def train_epoch_with_ewc(self, train_loader: DataLoader, epoch: int) -> Dict[str, float]:
        """
        使用EWC正则化训练一个epoch

        Args:
            train_loader: 训练数据加载器
            epoch: 当前epoch

        Returns:
            epoch_stats: epoch统计信息
        """
        self.model.train()
        total_loss = 0.0
        total_ce_loss = 0.0
        total_ewc_loss = 0.0
        correct = 0
        total = 0

        for batch_idx, (data, target) in enumerate(train_loader):
            data, target = data.to(self.device), target.to(self.device)

            self.optimizer.zero_grad()

            # 前向传播
            output = self.model(data)

            # 计算交叉熵损失
            ce_loss = self.criterion(output, target)

            # 计算EWC正则化惩罚
            ewc_loss = self.ewc_penalty()

            # 总损失
            total_loss_batch = ce_loss + self.ewc_lambda * ewc_loss

            # 反向传播
            total_loss_batch.backward()
            self.optimizer.step()

            # 统计
            total_loss += total_loss_batch.item()
            total_ce_loss += ce_loss.item()
            total_ewc_loss += ewc_loss.item()

            pred = output.argmax(dim=1)
            correct += pred.eq(target).sum().item()
            total += target.size(0)

        avg_total_loss = total_loss / len(train_loader)
        avg_ce_loss = total_ce_loss / len(train_loader)
        avg_ewc_loss = total_ewc_loss / len(train_loader)
        accuracy = 100.0 * correct / total

        return {
            'total_loss': avg_total_loss,
            'ce_loss': avg_ce_loss,
            'ewc_loss': avg_ewc_loss,
            'accuracy': accuracy,
            'correct': correct,
            'total': total
        }

    def get_method_info(self) -> Dict:
        """
        获取方法信息

        Returns:
            method_info: 方法信息字典
        """
        return {
            'name': self.method_name,
            'description': 'Elastic Weight Consolidation，使用Fisher信息矩阵保护重要参数',
            'advantages': [
                '理论基础扎实',
                '有效保护重要参数',
                '不需要存储旧数据'
            ],
            'disadvantages': [
                '需要计算Fisher信息矩阵',
                '内存开销较大',
                '超参数敏感'
            ],
            'parameters': {
                'learning_rate': self.learning_rate,
                'ewc_lambda': self.ewc_lambda,
                'fisher_sample_size': self.fisher_sample_size,
                'optimizer': type(self.optimizer).__name__,
                'criterion': type(self.criterion).__name__
            }
        }

    def analyze_forgetting(self, test_loaders: Dict[int, DataLoader]) -> Dict:
        """
        分析遗忘情况

        Args:
            test_loaders: 各任务的测试数据加载器字典

        Returns:
            forgetting_analysis: 遗忘分析结果
        """
        print(f"\n=== 分析遗忘情况 (EWC方法) ===")

        forgetting_analysis = {
            'method': self.method_name,
            'task_performances': {},
            'forgetting_metrics': {},
            'fisher_info': {}
        }

        # 评估每个任务的性能
        for task_id, test_loader in test_loaders.items():
            print(f"评估任务 {task_id} 性能...")
            performance = self.evaluate(test_loader)
            forgetting_analysis['task_performances'][task_id] = performance

            print(f"任务 {task_id}: 准确率 {performance['accuracy']:.2f}%")

        # 计算遗忘指标
        if len(forgetting_analysis['task_performances']) > 1:
            task_ids = sorted(forgetting_analysis['task_performances'].keys())

            # 计算平均遗忘
            accuracies = [forgetting_analysis['task_performances'][tid]['accuracy']
                         for tid in task_ids]

            forgetting_analysis['forgetting_metrics'] = {
                'average_accuracy': sum(accuracies) / len(accuracies),
                'accuracy_drop': accuracies[0] - accuracies[-1] if len(accuracies) > 1 else 0,
                'task_accuracies': dict(zip(task_ids, accuracies))
            }

            print(f"平均准确率: {forgetting_analysis['forgetting_metrics']['average_accuracy']:.2f}%")
            print(f"准确率下降: {forgetting_analysis['forgetting_metrics']['accuracy_drop']:.2f}%")

        # 记录Fisher信息矩阵统计
        for task_id in self.fisher_matrices:
            fisher_stats = {}
            for name, fisher in self.fisher_matrices[task_id].items():
                fisher_stats[name] = {
                    'mean': fisher.mean().item(),
                    'std': fisher.std().item(),
                    'max': fisher.max().item(),
                    'min': fisher.min().item()
                }
            forgetting_analysis['fisher_info'][task_id] = fisher_stats

        return forgetting_analysis


if __name__ == "__main__":
    # 测试EWC持续学习器
    print("测试EWC持续学习器...")

    # 创建简单的测试模型
    from ..models.cnn_model import SimpleCNN

    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = SimpleCNN(num_classes=10)

    # 创建EWC学习器
    learner = EWCContinualLearner(
        model, device,
        learning_rate=0.001,
        ewc_lambda=1000.0,
        fisher_sample_size=1000
    )

    # 打印方法信息
    method_info = learner.get_method_info()
    print(f"\n方法: {method_info['name']}")
    print(f"描述: {method_info['description']}")
    print(f"优点: {', '.join(method_info['advantages'])}")
    print(f"缺点: {', '.join(method_info['disadvantages'])}")

    print("EWC持续学习器测试完成！")
