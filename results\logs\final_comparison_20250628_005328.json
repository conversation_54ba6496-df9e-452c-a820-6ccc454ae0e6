{"methods": ["naive", "lwf"], "performance_comparison": {"naive": {"final_accuracy": 47.81, "average_task_accuracy": 49.17712404854968, "max_accuracy": 51.01, "accuracy_std": 1.5999999999999979}, "lwf": {"final_accuracy": 44.43, "average_task_accuracy": 45.36884085496635, "max_accuracy": 50.6, "accuracy_std": 3.085000000000001}, "best_final_accuracy": ["naive", {"final_accuracy": 47.81, "average_task_accuracy": 49.17712404854968, "max_accuracy": 51.01, "accuracy_std": 1.5999999999999979}], "best_average_accuracy": ["naive", {"final_accuracy": 47.81, "average_task_accuracy": 49.17712404854968, "max_accuracy": 51.01, "accuracy_std": 1.5999999999999979}]}, "forgetting_comparison": {"naive": {"average_forgetting": 99.2605565285075, "max_forgetting": 99.2605565285075, "forgetting_detected": true}, "lwf": {"average_forgetting": 86.86514886164623, "max_forgetting": 86.86514886164623, "forgetting_detected": true}, "least_forgetting": ["lwf", {"average_forgetting": 86.86514886164623, "max_forgetting": 86.86514886164623, "forgetting_detected": true}]}, "efficiency_comparison": {"naive": {"total_training_time": 166.38391494750977, "average_time_per_task": 83.19195747375488}, "lwf": {"total_training_time": 175.5230450630188, "average_time_per_task": 87.7615225315094}, "fastest_method": ["naive", {"total_training_time": 166.38391494750977, "average_time_per_task": 83.19195747375488}]}, "ranking": {"scores": {"naive": 0.20907872392843654, "lwf": 0.2171245534150613}, "sorted_ranking": [["lwf", 0.2171245534150613], ["naive", 0.20907872392843654]], "best_method": ["lwf", 0.2171245534150613], "weights_used": {"final_accuracy": 0.4, "average_forgetting": 0.3, "training_time": 0.3}}}